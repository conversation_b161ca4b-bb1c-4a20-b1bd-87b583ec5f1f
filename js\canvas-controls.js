/**
 * K<PERSON> Poster Maker - Canvas Controls
 * 畫布控制模塊
 */

class KMSCanvasControls {
    constructor(posterMaker) {
        this.posterMaker = posterMaker;
        this.init();
    }
    
    init() {
        this.setupCanvasControls();
        this.setupResponsiveCanvas();
        this.initializeZoomSlider();
    }
    
    setupCanvasControls() {
        // Paper size controls
        document.querySelectorAll('.kms-paper-option').forEach(option => {
            option.addEventListener('click', (e) => {
                const size = e.currentTarget.dataset.size;
                this.changePaperSize(size);
                
                // Update active state
                document.querySelectorAll('.kms-paper-option').forEach(opt => opt.classList.remove('active'));
                e.currentTarget.classList.add('active');
            });
        });
        
        // Canvas border controls
        document.querySelectorAll('.kms-canvas-border-option').forEach(option => {
            option.addEventListener('click', (e) => {
                const borderType = e.currentTarget.dataset.border;
                this.changeCanvasBorder(borderType);
                
                // Update active state
                document.querySelectorAll('.kms-canvas-border-option').forEach(opt => opt.classList.remove('active'));
                e.currentTarget.classList.add('active');
            });
        });
        
        // Grid toggle
        const toggleGridBtn = document.getElementById('toggleGridBtn');
        if (toggleGridBtn) {
            toggleGridBtn.addEventListener('click', () => this.toggleGrid());
        }
        
        // Background color
        const bgColorInput = document.getElementById('bgColor');
        if (bgColorInput) {
            const handleColorChange = (e) => {
                this.changeBackgroundColor(e.target.value);
            };
            bgColorInput.addEventListener('input', handleColorChange);
            bgColorInput.addEventListener('change', handleColorChange);
        }
        
        // Paper radius
        const paperRadius = document.getElementById('paperRadius');
        const paperRadiusValue = document.getElementById('paperRadiusValue');
        if (paperRadius && paperRadiusValue) {
            paperRadius.addEventListener('input', (e) => {
                const value = e.target.value + 'px';
                paperRadiusValue.textContent = value;
                this.changePaperRadius(value);
            });
        }
        
        // Zoom controls
        const zoomSlider = document.getElementById('zoomSlider');
        const zoomValue = document.getElementById('zoomValue');
        if (zoomSlider && zoomValue) {
            zoomSlider.addEventListener('input', (e) => {
                const scale = parseInt(e.target.value) / 100;
                this.setCanvasZoom(scale);
                zoomValue.textContent = e.target.value + '%';
            });
        }
    }
    
    changePaperSize(size) {
        this.posterMaker.currentPaperSize = size;
        const canvas = document.getElementById('posterCanvas');
        if (!canvas) return;
        
        // Remove existing size classes
        canvas.classList.remove('kms-canvas-letter', 'kms-canvas-4x6', 'kms-canvas-trading-card');
        
        const sizeConfig = {
            'letter': { width: '816px', height: '1056px', classes: ['kms-canvas-letter'] },
            'a4': { width: '794px', height: '1123px', classes: ['kms-canvas-a4'] },
            '4x6': { width: '576px', height: '384px', classes: ['kms-canvas-4x6'] },
            'trading-card': { width: '816px', height: '1056px', classes: ['kms-canvas-letter', 'kms-canvas-trading-card-layout'] }
        };
        
        if (sizeConfig[size]) {
            canvas.style.width = sizeConfig[size].width;
            canvas.style.height = sizeConfig[size].height;
            sizeConfig[size].classes.forEach(className => {
                canvas.classList.add(className);
            });
        }
        
        // Handle trading card layout
        if (size === 'trading-card') {
            // Show trading card layout options
            const tradingCardSection = document.getElementById('tradingCardLayoutSection');
            if (tradingCardSection) {
                tradingCardSection.style.display = 'block';
            }
            
            // Initialize trading card functionality if not already done
            if (window.kmsTradingCard) {
                window.kmsTradingCard.handlePaperSizeChange(size);
            }
        } else {
            // Hide trading card layout options
            const tradingCardSection = document.getElementById('tradingCardLayoutSection');
            if (tradingCardSection) {
                tradingCardSection.style.display = 'none';
            }
        }
        
        // 重新初始化所有現有元素的交互功能
        this.reinitializeElementInteractions();

        // Reposition elements to stay within bounds
        this.repositionElementsInBounds();

        // 更新 layer manager
        if (this.posterMaker.layerManager) {
            this.posterMaker.layerManager.updateLayerList();
        }

        // Update responsive scaling
        setTimeout(() => {
            this.handleCanvasResize();
        }, 100);

        // Show message
        this.showMessage(`Paper size changed to ${size.toUpperCase()}`);
    }

    showMessage(message) {
        // 創建臨時消息提示
        const messageDiv = document.createElement('div');
        messageDiv.style.cssText = `
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: var(--color-1);
            color: var(--text-color-2);
            padding: 16px 24px;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.3);
            z-index: 10000;
            font-weight: bold;
        `;
        messageDiv.textContent = message;

        document.body.appendChild(messageDiv);

        setTimeout(() => {
            if (messageDiv.parentNode) {
                messageDiv.parentNode.removeChild(messageDiv);
            }
        }, 2000);
    }

    // 重新初始化所有元素的交互功能
    reinitializeElementInteractions() {
        const canvas = document.getElementById('posterCanvas');
        if (!canvas) return;

        // 重新設置所有畫布元素的交互功能（包括 trading card 元素）
        const elements = canvas.querySelectorAll('.kms-canvas-element, .kms-text-element, .kms-image-element, .kms-qr-element, .kms-trading-card-text-element, .kms-trading-card-image-element, .kms-trading-card-qr-element');
        elements.forEach(element => {
            // 重新設置元素交互
            this.posterMaker.setupElementInteraction(element);

            // 重新添加 resize handles
            if ((element.classList.contains('kms-text-element') || element.classList.contains('kms-trading-card-text-element')) && this.posterMaker.textHandler) {
                this.posterMaker.textHandler.addResizeHandles(element);
            } else if ((element.classList.contains('kms-image-element') || element.classList.contains('kms-trading-card-image-element')) && this.posterMaker.imageHandler) {
                this.posterMaker.imageHandler.addResizeHandles(element);
            } else if ((element.classList.contains('kms-qr-element') || element.classList.contains('kms-trading-card-qr-element')) && this.posterMaker.qrGenerator) {
                this.posterMaker.qrGenerator.addResizeHandles(element);
            }

            // 確保元素在 elements 數組中
            if (!this.posterMaker.elements.includes(element)) {
                this.posterMaker.elements.push(element);
            }
        });

        // 更新 Layer Manager
        if (this.posterMaker.layerManager) {
            this.posterMaker.layerManager.updateLayerList();
        }
    }
    
    changeCanvasBorder(borderType) {
        const canvas = document.getElementById('posterCanvas');
        if (!canvas) return;
        
        const borderElement = canvas.querySelector('.kms-canvas-border') || document.createElement('div');
        borderElement.className = 'kms-canvas-border';
        if (borderType !== 'none') {
            borderElement.classList.add(`kms-border-${borderType}`);
        }
        
        if (!canvas.contains(borderElement)) {
            canvas.appendChild(borderElement);
        }
    }
    
    toggleGrid() {
        const grid = document.getElementById('canvasGrid');
        const gridToggleBtn = document.getElementById('toggleGridBtn');
        if (grid) {
            grid.classList.toggle('active');
        }
        if (gridToggleBtn) {
            gridToggleBtn.classList.toggle('active');
        }
    }
    
    changeBackgroundColor(color) {
        const canvas = document.getElementById('posterCanvas');
        if (canvas) {
            canvas.style.backgroundColor = color;
        }
        const bgColorPreview = document.querySelector('#bgColor + .kms-color-preview');
        if (bgColorPreview) {
            bgColorPreview.style.backgroundColor = color;
            bgColorPreview.dataset.color = color;
        }
    }
    
    changePaperRadius(radius) {
        const canvas = document.getElementById('posterCanvas');
        if (canvas) {
            canvas.style.setProperty('border-radius', radius, 'important');
        }
    }
    
    setCanvasZoom(scale) {
        const canvas = document.getElementById('posterCanvas');
        if (!canvas) return;
        
        // Clamp scale between 0.5 and 2.0
        const clampedScale = Math.max(0.5, Math.min(2.0, scale));
        
        // Apply zoom
        canvas.style.transform = `scale(${clampedScale})`;
        canvas.style.transformOrigin = 'center center';
        
        // Update slider and display
        const zoomSlider = document.getElementById('zoomSlider');
        const zoomValue = document.getElementById('zoomValue');
        if (zoomSlider && zoomValue) {
            const zoomPercent = Math.round(clampedScale * 100);
            zoomSlider.value = zoomPercent;
            zoomValue.textContent = zoomPercent + '%';
        }
    }
    
    initializeZoomSlider() {
        const canvas = document.getElementById('posterCanvas');
        if (!canvas) return;
        
        // Get current transform scale
        const transform = canvas.style.transform;
        let currentScale = 1;
        
        if (transform && transform.includes('scale')) {
            const scaleMatch = transform.match(/scale\(([^)]+)\)/);
            if (scaleMatch) {
                currentScale = parseFloat(scaleMatch[1]);
            }
        }
        
        // Update slider to match current scale
        this.setCanvasZoom(currentScale);
    }
    
    setupResponsiveCanvas() {
        this.handleCanvasResize();
        window.addEventListener('resize', () => this.handleCanvasResize());
    }
    
    handleCanvasResize() {
        const canvas = document.getElementById('posterCanvas');
        const container = document.querySelector('.kms-canvas-container');
        if (!canvas || !container) return;
        
        // Get container dimensions
        const containerRect = container.getBoundingClientRect();
        const containerWidth = containerRect.width - 40; // Account for padding
        const containerHeight = containerRect.height - 40;
        
        // Get canvas natural dimensions
        const canvasWidth = parseInt(canvas.style.width) || canvas.offsetWidth;
        const canvasHeight = parseInt(canvas.style.height) || canvas.offsetHeight;
        
        // Calculate scale to fit container
        const scaleX = containerWidth / canvasWidth;
        const scaleY = containerHeight / canvasHeight;
        const scale = Math.min(scaleX, scaleY, 1); // Don't scale up beyond 100%
        
        // Apply scaling if needed
        if (scale < 1) {
            canvas.style.transform = `scale(${scale})`;
            canvas.style.transformOrigin = 'center center';
        } else {
            // Reset transform if not needed
            canvas.style.transform = 'none';
        }
    }
    
    repositionElementsInBounds() {
        const canvas = document.getElementById('posterCanvas');
        if (!canvas) return;
        
        this.posterMaker.elements.forEach(element => {
            const rect = element.getBoundingClientRect();
            const canvasRect = canvas.getBoundingClientRect();
            
            let left = parseInt(element.style.left) || 0;
            let top = parseInt(element.style.top) || 0;
            
            if (left + rect.width > canvas.offsetWidth) {
                left = canvas.offsetWidth - rect.width;
            }
            if (top + rect.height > canvas.offsetHeight) {
                top = canvas.offsetHeight - rect.height;
            }
            if (left < 0) left = 0;
            if (top < 0) top = 0;
            
            element.style.left = left + 'px';
            element.style.top = top + 'px';
        });
    }
    
    setupCanvas() {
        const canvas = document.getElementById('posterCanvas');
        if (!canvas) return;
        
        // Set default paper size
        this.changePaperSize(this.posterMaker.currentPaperSize || 'letter');
        
        // Set default background color
        const bgColorInput = document.getElementById('bgColor');
        if (bgColorInput) {
            this.changeBackgroundColor(bgColorInput.value || '#ffffff');
        }
        
        // Set default paper radius
        const paperRadiusInput = document.getElementById('paperRadius');
        if (paperRadiusInput) {
            this.changePaperRadius(paperRadiusInput.value + 'px');
        }
        
        // Set active paper size option
        const activeOption = document.querySelector(`.kms-paper-option[data-size="${this.posterMaker.currentPaperSize}"]`);
        if (activeOption) {
            activeOption.classList.add('active');
        }
    }
    
    // Legacy method for backward compatibility
    zoomCanvas(factor) {
        const canvas = document.getElementById('posterCanvas');
        if (!canvas) return;
        
        const currentTransform = canvas.style.transform;
        let currentScale = 1;
        
        if (currentTransform && currentTransform.includes('scale')) {
            const scaleMatch = currentTransform.match(/scale\(([^)]+)\)/);
            if (scaleMatch) {
                currentScale = parseFloat(scaleMatch[1]);
            }
        }
        
        this.setCanvasZoom(currentScale * factor);
    }
}
