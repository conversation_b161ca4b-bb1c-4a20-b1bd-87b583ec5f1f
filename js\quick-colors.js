/**
 * <PERSON><PERSON> Poster Maker - Quick Colors
 * 快速顏色模塊
 */

class KMSQuickColors {
    constructor(posterMaker) {
        this.posterMaker = posterMaker;
        this.currentQuickColorTarget = 'text';
        this.init();
    }
    
    init() {
        // Wait for DOM to be fully loaded
        setTimeout(() => {
            this.initializeQuickColors();
            this.updateQuickColorDisplay();
        }, 100);
    }
    
    initializeQuickColors() {
        // Color target buttons
        document.querySelectorAll('.kms-color-target-btn').forEach(button => {
            button.addEventListener('click', (e) => {
                const target = e.currentTarget.dataset.target;
                this.setQuickColorTarget(target);
                document.querySelectorAll('.kms-color-target-btn').forEach(btn => btn.classList.remove('active'));
                e.currentTarget.classList.add('active');
            });
        });

        // Quick color buttons
        document.querySelectorAll('.kms-quick-color-btn').forEach(button => {
            button.addEventListener('click', (e) => {
                const color = e.currentTarget.dataset.color;
                if (this.posterMaker.selectedElement) {
                    this.applyQuickColor(color);
                    document.querySelectorAll('.kms-quick-color-btn').forEach(btn => btn.classList.remove('active'));
                    e.currentTarget.classList.add('active');
                } else {
                    // Show notification
                    this.showNotification(
                        this.posterMaker.currentLanguage === 'en' 
                            ? 'Please select an element first' 
                            : '請先選擇一個元素'
                    );
                }
            });
        });

        // Set default target button as active
        const defaultTargetBtn = document.querySelector('.kms-color-target-btn[data-target="text"]');
        if (defaultTargetBtn) {
            defaultTargetBtn.classList.add('active');
        }
    }

    setQuickColorTarget(target) {
        this.currentQuickColorTarget = target;
        this.updateQuickColorDisplay();
    }

    updateQuickColorDisplay() {
        const targetLabel = document.querySelector('.kms-current-target-label i');
        const targetName = document.querySelector('.kms-current-target-name');
        if (!targetLabel || !targetName) return;

        const targetConfig = {
            text: { icon: 'fas fa-font', key: 'textColor' },
            border: { icon: 'fas fa-border-style', key: 'textBorder' },
            background: { icon: 'fas fa-fill', key: 'textBackground' },
            shadow: { icon: 'fas fa-adjust', key: 'textShadow' },
            imageBorder: { icon: 'fas fa-image', key: 'imageBorder' }
        };

        const config = targetConfig[this.currentQuickColorTarget];
        if (config) {
            targetLabel.className = config.icon;
            const translations = this.posterMaker.getTranslations();
            if (translations[config.key]) {
                targetName.textContent = translations[config.key];
            }
        }
    }

    applyQuickColor(color) {
        if (!this.posterMaker.selectedElement) return;

        const colorMappings = {
            text: { id: 'fontColor', handler: 'updateSelectedTextColor' },
            border: { id: 'borderColor', handler: 'updateSelectedTextBorder' },
            background: { id: 'textBgColor', handler: 'updateSelectedTextBackground' },
            shadow: { id: 'shadowColor', handler: 'updateSelectedTextShadow' }
        };

        if (this.currentQuickColorTarget === 'imageBorder') {
            // Handle image border color
            const borderColorPicker = document.getElementById('imageBorderColor');
            if (borderColorPicker) {
                borderColorPicker.value = color;
                this.updateColorValueDisplay(borderColorPicker, color);
                // Apply to selected image element if applicable
                this.applyImageBorderColor(color);
            }
        } else {
            const mapping = colorMappings[this.currentQuickColorTarget];
            if (mapping) {
                const colorPicker = document.getElementById(mapping.id);
                if (colorPicker) {
                    colorPicker.value = color;
                    if (this.posterMaker.textHandler) {
                        this.posterMaker.textHandler.updateColorPreview(colorPicker, color);
                        
                        // Handle different color application logic
                        if (mapping.handler === 'updateSelectedTextBorder') {
                            this.posterMaker.textHandler.updateSelectedTextBorder('borderColor', color);
                        } else if (mapping.handler === 'updateSelectedTextBackground') {
                            this.posterMaker.textHandler.updateSelectedTextBackground();
                        } else if (mapping.handler === 'updateSelectedTextShadow') {
                            this.posterMaker.textHandler.updateTextShadow();
                        } else {
                            this.posterMaker.textHandler.updateSelectedTextStyle('color', color);
                        }
                    }
                }
            }
        }
    }

    applyImageBorderColor(color) {
        if (!this.posterMaker.selectedElement) return;
        
        if (this.posterMaker.selectedElement.classList.contains('kms-image-element')) {
            this.posterMaker.selectedElement.style.borderColor = color;
        }
    }

    updateColorValueDisplay(colorPicker, color) {
        const colorRow = colorPicker.closest('.kms-enhanced-color-row');
        if (colorRow) {
            const colorValue = colorRow.querySelector('.kms-color-value');
            if (colorValue) {
                colorValue.textContent = color.toUpperCase();
            }
        }
    }

    showNotification(message) {
        // Create notification element
        const notification = document.createElement('div');
        notification.className = 'kms-notification';
        notification.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: #ff6b6b;
            color: white;
            padding: 12px 20px;
            border-radius: 4px;
            z-index: 10000;
            font-size: 14px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            animation: slideInRight 0.3s ease-out;
        `;
        
        notification.textContent = message;
        document.body.appendChild(notification);
        
        // Auto remove after 3 seconds
        setTimeout(() => {
            notification.style.animation = 'slideOutRight 0.3s ease-in';
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.parentNode.removeChild(notification);
                }
            }, 300);
        }, 3000);
    }

    // Color palette data for reference
    getColorPalette() {
        return {
            grays: [
                '#FFFFFF', '#F5F5F5', '#E0E0E0', '#CCCCCC', '#B8B8B8',
                '#A0A0A0', '#808080', '#606060', '#404040', '#000000'
            ],
            reds: [
                '#FF0000', '#FF4500', '#FF6B6B', '#DC143C', '#B22222',
                '#8B0000', '#FF69B4', '#FF1493', '#F8BBD9', '#FFB6C1'
            ],
            orangesYellowsGreens: [
                '#FFA500', '#FFB347', '#FFFF00', '#FFD700', '#FFEAA7',
                '#ADFF2F', '#32CD32', '#00FF00', '#228B22', '#96CEB4'
            ],
            bluesPurples: [
                '#00FFFF', '#00CED1', '#4ECDC4', '#87CEEB', '#45B7D1',
                '#0000FF', '#4169E1', '#8A2BE2', '#9932CC', '#DDA0DD'
            ]
        };
    }

    // Get color name for tooltip
    getColorName(color) {
        const colorNames = {
            '#FFFFFF': 'White',
            '#F5F5F5': 'Light Gray',
            '#E0E0E0': 'Silver',
            '#CCCCCC': 'Light Gray',
            '#B8B8B8': 'Gray',
            '#A0A0A0': 'Gray',
            '#808080': 'Gray',
            '#606060': 'Dark Gray',
            '#404040': 'Dark Gray',
            '#000000': 'Black',
            '#FF0000': 'Red',
            '#FF4500': 'Orange Red',
            '#FF6B6B': 'Light Red',
            '#DC143C': 'Crimson',
            '#B22222': 'Fire Brick',
            '#8B0000': 'Dark Red',
            '#FF69B4': 'Hot Pink',
            '#FF1493': 'Deep Pink',
            '#F8BBD9': 'Pink',
            '#FFB6C1': 'Light Pink',
            '#FFA500': 'Orange',
            '#FFB347': 'Light Orange',
            '#FFFF00': 'Yellow',
            '#FFD700': 'Gold',
            '#FFEAA7': 'Light Yellow',
            '#ADFF2F': 'Green Yellow',
            '#32CD32': 'Lime Green',
            '#00FF00': 'Lime',
            '#228B22': 'Forest Green',
            '#96CEB4': 'Light Green',
            '#00FFFF': 'Cyan',
            '#00CED1': 'Dark Turquoise',
            '#4ECDC4': 'Teal',
            '#87CEEB': 'Sky Blue',
            '#45B7D1': 'Light Blue',
            '#0000FF': 'Blue',
            '#4169E1': 'Royal Blue',
            '#8A2BE2': 'Blue Violet',
            '#9932CC': 'Dark Orchid',
            '#DDA0DD': 'Plum'
        };
        
        return colorNames[color.toUpperCase()] || color;
    }

    // Initialize color tooltips
    initializeColorTooltips() {
        document.querySelectorAll('.kms-quick-color-btn').forEach(button => {
            const color = button.dataset.color;
            const colorName = this.getColorName(color);
            button.title = colorName;
            
            const tooltip = button.querySelector('.kms-color-tooltip');
            if (tooltip) {
                tooltip.textContent = colorName;
            }
        });
    }
}
