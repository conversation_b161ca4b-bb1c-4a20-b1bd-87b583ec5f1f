/**
 * K<PERSON> Poster Maker - Print Preview
 * 打印預覽模塊
 */

class KMSPrintPreview {
    constructor(posterMaker) {
        this.posterMaker = posterMaker;
    }
    
    showPrintPreview() {
        // Create preview window with appropriate size
        const canvas = document.getElementById('posterCanvas');
        const canvasRect = canvas.getBoundingClientRect();
        const windowWidth = Math.max(900, canvasRect.width + 100);
        const windowHeight = Math.max(700, canvasRect.height + 150);
        
        const previewWindow = window.open('', '_blank', `width=${windowWidth},height=${windowHeight}`);
        
        if (!previewWindow) {
            alert(this.posterMaker.currentLanguage === 'en' 
                ? 'Please allow popups to show print preview'
                : '請允許彈出窗口以顯示打印預覽');
            return;
        }
        
        // Clone the canvas and all its content
        const canvasClone = canvas.cloneNode(true);
        
        // Reset any transform scaling on the cloned canvas
        canvasClone.style.transform = 'none';
        canvasClone.style.transformOrigin = 'initial';
        
        // Reset transform on all child elements and preserve original styles
        canvasClone.querySelectorAll('.kms-canvas-element, .kms-trading-card-text-element, .kms-trading-card-image-element, .kms-trading-card-qr-element').forEach(element => {
            element.style.transform = 'none';
            element.style.transformOrigin = 'initial';
            
            // For text elements, ensure all styles are preserved as inline styles
            if (element.classList.contains('kms-text-element') || element.classList.contains('kms-trading-card-text-element')) {
                const originalElement = canvas.querySelector(`[data-element-id="${element.dataset.elementId}"]`);
                if (originalElement) {
                    // Copy all computed styles to inline styles
                    const computedStyle = window.getComputedStyle(originalElement);
                    [
                        'fontFamily', 'fontSize', 'fontWeight', 'fontStyle', 'textDecoration',
                        'color', 'backgroundColor', 'border', 'borderRadius', 'padding',
                        'textAlign', 'lineHeight', 'textShadow', 'opacity'
                    ].forEach(prop => {
                        element.style[prop] = computedStyle[prop];
                    });
                }
            }
            
            // For image elements, ensure proper sizing
            if (element.classList.contains('kms-image-element') || element.classList.contains('kms-trading-card-image-element')) {
                const img = element.querySelector('img');
                if (img) {
                    img.style.width = '100%';
                    img.style.height = '100%';
                    img.style.objectFit = 'contain';
                    img.style.display = 'block';
                }
            }
        });
        
        // Remove any selection indicators and resize handles
        canvasClone.querySelectorAll('.kms-canvas-element').forEach(element => {
            element.classList.remove('selected');
        });
        canvasClone.querySelectorAll('.kms-resize-handle').forEach(handle => {
            handle.remove();
        });
        
        // Remove trading card labels (Card 1, Card 2, etc.) for print preview
        canvasClone.querySelectorAll('.kms-trading-card-label').forEach(label => {
            label.remove();
        });
        
        // Remove trading card borders for print preview
        canvasClone.querySelectorAll('.kms-trading-card').forEach(card => {
            card.style.border = 'none';
            card.style.background = 'transparent';
        });
        
        const canvasHTML = canvasClone.outerHTML;
        
        previewWindow.document.write(this.generatePreviewHTML(canvasHTML));
        previewWindow.document.close();
    }
    
    generatePreviewHTML(canvasHTML) {
        return `
            <!DOCTYPE html>
            <html>
            <head>
                <title>${this.posterMaker.currentLanguage === 'en' ? 'Print Preview' : '打印預覽'}</title>
                <style>
                    ${this.getPreviewStyles()}
                </style>
            </head>
            <body>
                <div class="print-controls">
                    <button class="btn btn-primary" onclick="window.print()">
                        ${this.posterMaker.currentLanguage === 'en' ? 'Print' : '打印'}
                    </button>
                    <button class="btn btn-secondary" onclick="window.close()">
                        ${this.posterMaker.currentLanguage === 'en' ? 'Close' : '關閉'}
                    </button>
                </div>
                <div class="preview-container">
                    ${canvasHTML}
                </div>
            </body>
            </html>
        `;
    }
    
    getPreviewStyles() {
        return `
            body {
                margin: 0;
                padding: 20px;
                font-family: Arial, sans-serif;
                background: #f5f5f5;
                overflow: auto;
            }
            .preview-container {
                display: flex;
                justify-content: center;
                align-items: flex-start;
                min-height: calc(100vh - 40px);
                padding: 60px 20px 20px 20px;
            }
            .kms-poster-canvas {
                background: white;
                box-shadow: 0 10px 30px rgba(0,0,0,0.1);
                border-radius: 8px;
                position: relative;
                overflow: hidden;
            }
            .kms-canvas-letter {
                width: 816px !important;
                height: 1056px !important;
                max-width: none !important;
                max-height: none !important;
            }
            .kms-canvas-4x6 {
                width: 576px !important;
                height: 384px !important;
                max-width: none !important;
                max-height: none !important;
            }
            .kms-canvas-trading-card-layout {
                width: 816px !important;
                height: 1056px !important;
                max-width: none !important;
                max-height: none !important;
            }
            /* 4x6 Trading Card Layout */
            .kms-canvas-4x6.kms-canvas-trading-card-layout,
            .kms-canvas-4x6-landscape.kms-canvas-trading-card-layout {
                width: 576px !important;
                height: 384px !important;
                max-width: none !important;
                max-height: none !important;
            }
            .kms-canvas-4x6-portrait.kms-canvas-trading-card-layout {
                width: 384px !important;
                height: 576px !important;
                max-width: none !important;
                max-height: none !important;
            }
            /* Trading Card specific styles */
            .kms-trading-card {
                position: absolute;
                border: 1px solid #ddd;
                background: white;
                box-sizing: border-box;
            }
            .kms-trading-card-content {
                position: relative;
                width: 100%;
                height: 100%;
                overflow: hidden;
            }
            .kms-trading-card-text-element,
            .kms-trading-card-image-element,
            .kms-trading-card-qr-element {
                position: absolute;
            }
            /* Ensure all canvas content is visible */
            .kms-poster-canvas * {
                max-width: none !important;
                max-height: none !important;
            }
            .kms-canvas-element {
                position: absolute;
            }
            .kms-text-element {
                word-wrap: break-word;
                white-space: pre-wrap;
                box-sizing: border-box !important;
                transform: none !important;
                zoom: 1 !important;
                scale: 1 !important;
            }
            .kms-image-element img {
                width: 100%;
                height: 100%;
                object-fit: contain;
            }
            .kms-qr-element img {
                display: block;
                width: 100%;
                height: auto;
            }
            .kms-resize-handle,
            .kms-canvas-element.selected::after {
                display: none !important;
            }
            .print-controls {
                position: fixed;
                top: 20px;
                right: 20px;
                display: flex;
                gap: 10px;
            }
            .btn {
                padding: 10px 20px;
                border: none;
                border-radius: 5px;
                cursor: pointer;
                font-size: 14px;
            }
            .btn-primary {
                background: #667eea;
                color: white;
            }
            .btn-secondary {
                background: #6c757d;
                color: white;
            }
            @media print {
                @page {
                    margin: 0 !important;
                    padding: 0 !important;
                    size: auto !important;
                    -webkit-print-color-adjust: exact !important;
                    color-adjust: exact !important;
                    print-color-adjust: exact !important;
                }
                
                @page :first {
                    margin: 0 !important;
                    padding: 0 !important;
                }
                
                @page :left {
                    margin: 0 !important;
                    padding: 0 !important;
                }
                
                @page :right {
                    margin: 0 !important;
                    padding: 0 !important;
                }
                * {
                    -webkit-print-color-adjust: exact !important;
                    color-adjust: exact !important;
                    print-color-adjust: exact !important;
                }
                html {
                    margin: 0 !important;
                    padding: 0 !important;
                    background: transparent !important;
                }
                .print-controls {
                    display: none !important;
                }
                body {
                    margin: 0 !important;
                    padding: 0 !important;
                    background: transparent !important;
                    -webkit-print-color-adjust: exact !important;
                    color-adjust: exact !important;
                    print-color-adjust: exact !important;
                }
                .preview-container {
                    margin: 0 !important;
                    padding: 0 !important;
                    min-height: auto !important;
                }
                .kms-poster-canvas {
                    box-shadow: none !important;
                    border-radius: 0 !important;
                    margin: 0 !important;
                    padding: 0 !important;
                    border: none !important;
                    outline: none !important;
                    -webkit-print-color-adjust: exact !important;
                    color-adjust: exact !important;
                    print-color-adjust: exact !important;
                }
            }
        `;
    }
    
    // Export poster as image
    exportAsImage(format = 'png', quality = 0.9) {
        const canvas = document.getElementById('posterCanvas');
        if (!canvas) return;

        // Create a new canvas for export
        const exportCanvas = document.createElement('canvas');
        const ctx = exportCanvas.getContext('2d');

        // Set canvas size based on paper size
        const scale = 2; // Higher resolution

        if (this.posterMaker.currentPaperSize === 'letter') {
            exportCanvas.width = 816 * scale;
            exportCanvas.height = 1056 * scale;
        } else if (canvas && canvas.classList.contains('kms-canvas-4x6-landscape')) {
            // 4x6 橫向
            exportCanvas.width = 576 * scale;
            exportCanvas.height = 384 * scale;
        } else {
            // 4x6 直向 (預設)
            exportCanvas.width = 384 * scale;
            exportCanvas.height = 576 * scale;
        }
        
        ctx.scale(scale, scale);
        
        // Fill background
        const bgColor = canvas.style.backgroundColor || '#ffffff';
        ctx.fillStyle = bgColor;
        ctx.fillRect(0, 0, exportCanvas.width / scale, exportCanvas.height / scale);
        
        // This would require html2canvas library for full implementation
        // For now, we'll use a simpler approach
        this.downloadCanvasAsImage(exportCanvas, format, quality);
    }
    
    downloadCanvasAsImage(canvas, format, quality) {
        const link = document.createElement('a');
        link.download = `poster.${format}`;
        link.href = canvas.toDataURL(`image/${format}`, quality);
        link.click();
    }
}
