/**
 * K<PERSON> Poster Maker - Image Handler
 * 圖片處理模塊
 */

class KMSImageHandler {
    constructor(posterMaker) {
        this.posterMaker = posterMaker;
        this.init();
    }
    
    init() {
        this.setupImageControls();
        this.setupDragAndDrop();
        this.initializeRotationButton();
    }
    
    setupImageControls() {
        // Image upload button
        const imageUpload = document.getElementById('imageUpload');
        const imageInput = document.getElementById('imageInput');
        
        if (imageUpload && imageInput) {
            imageUpload.addEventListener('click', () => {
                imageInput.click();
            });
            
            imageInput.addEventListener('change', (e) => {
                this.handleImageUpload(e.target.files[0]);
            });
        }
    }
    
    setupDragAndDrop() {
        const imageUpload = document.getElementById('imageUpload');
        const canvas = document.getElementById('posterCanvas');
        
        if (imageUpload) {
            imageUpload.addEventListener('dragover', (e) => {
                e.preventDefault();
                imageUpload.classList.add('dragover');
            });
            
            imageUpload.addEventListener('dragleave', (e) => {
                e.preventDefault();
                imageUpload.classList.remove('dragover');
            });
            
            imageUpload.addEventListener('drop', (e) => {
                e.preventDefault();
                imageUpload.classList.remove('dragover');
                
                const files = e.dataTransfer.files;
                if (files.length > 0 && this.isValidImageFile(files[0])) {
                    this.handleImageUpload(files[0]);
                }
            });
        }
        
        // Also allow dropping directly on canvas
        if (canvas) {
            canvas.addEventListener('dragover', (e) => {
                e.preventDefault();
            });
            
            canvas.addEventListener('drop', (e) => {
                e.preventDefault();
                const files = e.dataTransfer.files;
                if (files.length > 0 && this.isValidImageFile(files[0])) {
                    const rect = canvas.getBoundingClientRect();
                    const x = e.clientX - rect.left;
                    const y = e.clientY - rect.top;
                    this.handleImageUpload(files[0], { x, y });
                }
            });
        }
    }
    
    isValidImageFile(file) {
        const validTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'];
        return validTypes.includes(file.type);
    }
    
    handleImageUpload(file, position = null) {
        if (!this.isValidImageFile(file)) {
            alert(this.posterMaker.currentLanguage === 'en' 
                ? 'Please select a valid image file (JPG, PNG, GIF, WebP)' 
                : '請選擇有效的圖片文件 (JPG, PNG, GIF, WebP)');
            return;
        }
        
        // 顯示上傳進度
        this.showUploadProgress(true);
        
        // 同時保存到服務器和創建元素
        this.saveImageToServer(file).then(serverResponse => {
            // 使用服務器返回的URL創建圖片元素
            if (serverResponse.status === 200 && serverResponse.data) {
                this.createImageElement(serverResponse.data.url, position, {
                    filename: serverResponse.data.filename,
                    serverPath: serverResponse.data.url
                });
                this.showUploadSuccess(serverResponse.data.filename);
            } else {
                // 如果服務器保存失敗，仍然使用本地DataURL
                const reader = new FileReader();
                reader.onload = (e) => {
                    this.createImageElement(e.target.result, position);
                    this.showUploadWarning();
                };
                reader.readAsDataURL(file);
            }
        }).catch(error => {
            console.error('Server upload failed:', error);
            // 服務器上傳失敗時，使用本地DataURL
            const reader = new FileReader();
            reader.onload = (e) => {
                this.createImageElement(e.target.result, position);
                this.showUploadWarning();
            };
            reader.readAsDataURL(file);
        }).finally(() => {
            this.showUploadProgress(false);
        });
    }
    
    createImageElement(src, position = null, serverInfo = null) {
        // 獲取應該添加元素的容器
        const container = window.kmsTradingCard?.getElementContainer() || document.getElementById('posterCanvas');
        if (!container) {
            if (this.posterMaker && this.posterMaker.showMessage) {
                this.posterMaker.showMessage('無法添加圖片：請先選擇編輯模式或確保畫布可用');
            }
            return null;
        }
        
        const imageContainer = document.createElement('div');

        // 根據容器類型設置不同的類名
        const isInTradingCard = container.classList.contains('kms-trading-card-content');
        if (isInTradingCard) {
            imageContainer.className = 'kms-trading-card-image-element';
        } else {
            imageContainer.className = 'kms-canvas-element kms-image-element';
        }

        imageContainer.dataset.elementId = `image_${++this.posterMaker.elementCounter}`;
        
        // 保存服務器路徑信息
        if (serverInfo) {
            imageContainer.dataset.serverFilename = serverInfo.filename;
            imageContainer.dataset.serverPath = serverInfo.serverPath;
            imageContainer.dataset.isServerImage = 'true';
        }
        
        const img = document.createElement('img');
        img.src = src;
        img.draggable = false;
        
        imageContainer.appendChild(img);
        
        // Function to setup image size and position
        const setupImageElement = () => {
            const canvas = container.id === 'posterCanvas' ? container : document.getElementById('posterCanvas');
            const maxWidth = Math.min(300, canvas ? canvas.offsetWidth * 0.4 : 300);
            const maxHeight = Math.min(300, canvas ? canvas.offsetHeight * 0.4 : 300);
            
            let width = img.naturalWidth || img.width || 200;
            let height = img.naturalHeight || img.height || 200;
            
            // Scale down if too large
            if (width > maxWidth || height > maxHeight) {
                const ratio = Math.min(maxWidth / width, maxHeight / height);
                width *= ratio;
                height *= ratio;
            }
            
            imageContainer.style.width = width + 'px';
            imageContainer.style.height = height + 'px';
            
            // Set position
            if (position) {
                const containerWidth = container.offsetWidth || (canvas ? canvas.offsetWidth : 800);
                const containerHeight = container.offsetHeight || (canvas ? canvas.offsetHeight : 600);
                imageContainer.style.left = Math.max(0, Math.min(position.x, containerWidth - width)) + 'px';
                imageContainer.style.top = Math.max(0, Math.min(position.y, containerHeight - height)) + 'px';
            } else {
                imageContainer.style.left = '50px';
                imageContainer.style.top = '50px';
            }
            
            // Add resize handles
            this.addResizeHandles(imageContainer);
        };

        // Set initial position and size
        if (img.complete && img.naturalWidth > 0) {
            // Image is already loaded
            setupImageElement();
        } else {
            // Wait for image to load
            img.onload = setupImageElement;
            img.onerror = () => {
                // Fallback for broken images
                imageContainer.style.width = '200px';
                imageContainer.style.height = '200px';
                imageContainer.style.left = '50px';
                imageContainer.style.top = '50px';
                this.addResizeHandles(imageContainer);
            };
        }
        
        // Setup element interaction (drag and select) - 使用主要的拖曳系統
        this.posterMaker.setupElementInteraction(imageContainer);
        
        // Setup image-specific interactions
        this.setupImageInteractions(imageContainer);
        
        // 直接添加到容器中
        if (isInTradingCard) {
            container.appendChild(imageContainer);

            // 添加到 elements 數組中以支持 Layer List
            if (!this.posterMaker.elements.includes(imageContainer)) {
                this.posterMaker.elements.push(imageContainer);
            }

            // 更新 Layer Manager
            if (this.posterMaker.layerManager) {
                this.posterMaker.layerManager.onElementAdded(imageContainer);
            }
        } else {
            // 使用主要的 poster maker 方法添加元素（包括圖層管理）
            this.posterMaker.addElementToCanvas(imageContainer);
        }

        // 移除自動選中邏輯，避免干擾其他已選中的元素
        // this.posterMaker.selectElement(imageContainer);

        // 如果是在 trading card 模式且是同步模式，觸發同步
        if (isInTradingCard && window.kmsTradingCard?.editMode === 'sync') {
            // 延遲觸發同步，讓元素先完全添加
            setTimeout(() => {
                window.kmsTradingCard.syncCard1ToAll();
            }, 100);
        }

        return imageContainer;
    }
    
    setupImageInteractions(imageElement) {
        // Double-click to replace image
        imageElement.addEventListener('dblclick', (e) => {
            e.stopPropagation();
            const input = document.createElement('input');
            input.type = 'file';
            input.accept = 'image/*';
            input.onchange = (e) => {
                if (e.target.files[0]) {
                    this.replaceImage(imageElement, e.target.files[0]);
                }
            };
            input.click();
        });
        
        // Context menu for image options
        imageElement.addEventListener('contextmenu', (e) => {
            e.preventDefault();
            this.showImageContextMenu(e, imageElement);
        });
    }
    
    replaceImage(imageElement, file) {
        const reader = new FileReader();
        reader.onload = (e) => {
            const img = imageElement.querySelector('img');
            if (img) {
                img.src = e.target.result;
            }
        };
        reader.readAsDataURL(file);
    }
    
    addResizeHandles(element) {
        const handles = ['nw', 'ne', 'sw', 'se', 'n', 's', 'w', 'e'];
        
        handles.forEach(direction => {
            const handle = document.createElement('div');
            handle.className = `kms-resize-handle ${direction}`;
            handle.addEventListener('mousedown', (e) => {
                e.stopPropagation();
                this.startResize(e, element, direction);
            });
            element.appendChild(handle);
        });
    }
    
    startResize(e, element, direction) {
        e.preventDefault();
        
        const startX = e.clientX;
        const startY = e.clientY;
        const startWidth = parseInt(window.getComputedStyle(element).width);
        const startHeight = parseInt(window.getComputedStyle(element).height);
        const startLeft = parseInt(element.style.left);
        const startTop = parseInt(element.style.top);
        
        const canvas = document.getElementById('posterCanvas');
        const canvasRect = canvas.getBoundingClientRect();
        
        const handleResize = (e) => {
            const deltaX = e.clientX - startX;
            const deltaY = e.clientY - startY;
            
            let newWidth = startWidth;
            let newHeight = startHeight;
            let newLeft = startLeft;
            let newTop = startTop;
            
            // Calculate new dimensions based on direction
            switch (direction) {
                case 'se':
                    newWidth = Math.max(50, startWidth + deltaX);
                    newHeight = Math.max(50, startHeight + deltaY);
                    break;
                case 'sw':
                    newWidth = Math.max(50, startWidth - deltaX);
                    newHeight = Math.max(50, startHeight + deltaY);
                    newLeft = startLeft + deltaX;
                    break;
                case 'ne':
                    newWidth = Math.max(50, startWidth + deltaX);
                    newHeight = Math.max(50, startHeight - deltaY);
                    newTop = startTop + deltaY;
                    break;
                case 'nw':
                    newWidth = Math.max(50, startWidth - deltaX);
                    newHeight = Math.max(50, startHeight - deltaY);
                    newLeft = startLeft + deltaX;
                    newTop = startTop + deltaY;
                    break;
                case 'e':
                    newWidth = Math.max(50, startWidth + deltaX);
                    break;
                case 'w':
                    newWidth = Math.max(50, startWidth - deltaX);
                    newLeft = startLeft + deltaX;
                    break;
                case 's':
                    newHeight = Math.max(50, startHeight + deltaY);
                    break;
                case 'n':
                    newHeight = Math.max(50, startHeight - deltaY);
                    newTop = startTop + deltaY;
                    break;
            }
            
            // Constrain to canvas bounds
            newLeft = Math.max(0, Math.min(newLeft, canvas.offsetWidth - newWidth));
            newTop = Math.max(0, Math.min(newTop, canvas.offsetHeight - newHeight));
            newWidth = Math.min(newWidth, canvas.offsetWidth - newLeft);
            newHeight = Math.min(newHeight, canvas.offsetHeight - newTop);
            
            // Apply new dimensions
            element.style.width = newWidth + 'px';
            element.style.height = newHeight + 'px';
            element.style.left = newLeft + 'px';
            element.style.top = newTop + 'px';
        };
        
        const stopResize = () => {
            document.removeEventListener('mousemove', handleResize);
            document.removeEventListener('mouseup', stopResize);
        };
        
        document.addEventListener('mousemove', handleResize);
        document.addEventListener('mouseup', stopResize);
    }
    
    showImageContextMenu(e, imageElement) {
        // Remove existing context menu
        const existingMenu = document.querySelector('.kms-context-menu');
        if (existingMenu) {
            existingMenu.remove();
        }
        
        const menu = document.createElement('div');
        menu.className = 'kms-context-menu';
        menu.style.position = 'fixed';
        menu.style.left = e.clientX + 'px';
        menu.style.top = e.clientY + 'px';
        menu.style.background = 'white';
        menu.style.border = '1px solid #ccc';
        menu.style.borderRadius = '4px';
        menu.style.boxShadow = '0 2px 10px rgba(0,0,0,0.1)';
        menu.style.zIndex = '1000';
        menu.style.minWidth = '150px';
        
        const options = [
            {
                text: this.posterMaker.currentLanguage === 'en' ? 'Replace Image' : '替換圖片',
                action: () => {
                    const input = document.createElement('input');
                    input.type = 'file';
                    input.accept = 'image/*';
                    input.onchange = (e) => {
                        if (e.target.files[0]) {
                            this.replaceImage(imageElement, e.target.files[0]);
                        }
                    };
                    input.click();
                }
            },
            {
                text: this.posterMaker.currentLanguage === 'en' ? 'Add Border Radius' : '添加圓角',
                action: () => this.showBorderRadiusControl(imageElement)
            },
            {
                text: this.posterMaker.currentLanguage === 'en' ? 'Delete Image' : '刪除圖片',
                action: () => {
                    imageElement.remove();
                    this.posterMaker.elements = this.posterMaker.elements.filter(el => el !== imageElement);
                    this.posterMaker.selectedElement = null;
                }
            }
        ];
        
        options.forEach(option => {
            const item = document.createElement('div');
            item.textContent = option.text;
            item.style.padding = '8px 12px';
            item.style.cursor = 'pointer';
            item.style.borderBottom = '1px solid #eee';
            item.addEventListener('click', () => {
                option.action();
                menu.remove();
            });
            item.addEventListener('mouseenter', () => {
                item.style.backgroundColor = '#f0f0f0';
            });
            item.addEventListener('mouseleave', () => {
                item.style.backgroundColor = 'white';
            });
            menu.appendChild(item);
        });
        
        document.body.appendChild(menu);
        
        // Remove menu when clicking elsewhere
        setTimeout(() => {
            document.addEventListener('click', () => {
                menu.remove();
            }, { once: true });
        }, 100);
    }
    
    showBorderRadiusControl(imageElement) {
        const img = imageElement.querySelector('img');
        if (!img) return;
        
        const currentRadius = parseInt(img.style.borderRadius) || 0;
        const newRadius = prompt(
            this.posterMaker.currentLanguage === 'en' 
                ? `Enter border radius (0-50px). Current: ${currentRadius}px`
                : `輸入圓角半徑 (0-50px)。當前: ${currentRadius}px`,
            currentRadius
        );
        
        if (newRadius !== null && !isNaN(newRadius)) {
            const radius = Math.max(0, Math.min(50, parseInt(newRadius)));
            img.style.borderRadius = radius + 'px';
        }
    }
    
    // Update controls when an image element is selected
    updateControlsForImageElement(element) {
        // 不再直接操作控制面板，讓統一的控制面板系統處理
        // 這樣可以確保自動滾動功能正常工作
        
        // 可以在這裡添加圖片特定的控制邏輯
        // 例如更新圖片相關的控制項狀態
        
        console.log('圖片控制面板已更新');
    }
    
    // 保存圖片到服務器
    async saveImageToServer(file) {
        try {
            console.log('=== 開始上傳圖片到服務器 ===');
            console.log('檔案信息:', {
                name: file.name,
                size: file.size,
                type: file.type,
                lastModified: file.lastModified
            });
            
            const formData = new FormData();
            formData.append('image', file);
            
            console.log('FormData 內容:', formData.get('image'));
            
            const response = await fetch('php/upload_image.php', {
                method: 'POST',
                body: formData
            });
            
            console.log('響應狀態:', response.status, response.statusText);
            
            // 檢查響應是否為JSON
            const contentType = response.headers.get('content-type');
            if (!contentType || !contentType.includes('application/json')) {
                const text = await response.text();
                console.error('Server returned non-JSON response:', text);
                throw new Error('服務器返回了無效的響應格式');
            }
            
            const result = await response.json();
            
            if (!response.ok || result.status !== 200) {
                throw new Error(result.message || `HTTP error! status: ${response.status}`);
            }
            
            return result;
            
        } catch (error) {
            console.error('Error uploading image to server:', error);
            throw error;
        }
    }
    
    // 顯示上傳進度
    showUploadProgress(show) {
        let progressElement = document.getElementById('uploadProgress');
        
        if (show) {
            if (!progressElement) {
                progressElement = document.createElement('div');
                progressElement.id = 'uploadProgress';
                progressElement.className = 'kms-upload-progress';
                progressElement.innerHTML = `
                    <div class="kms-progress-content">
                        <div class="kms-progress-spinner"></div>
                        <span>${this.posterMaker.currentLanguage === 'en' ? 'Uploading image...' : '正在上傳圖片...'}</span>
                    </div>
                `;
                document.body.appendChild(progressElement);
            }
            progressElement.style.display = 'flex';
        } else {
            if (progressElement) {
                progressElement.style.display = 'none';
            }
        }
    }
    
    // 顯示上傳成功消息
    showUploadSuccess(filename) {
        this.showToast(
            this.posterMaker.currentLanguage === 'en' 
                ? `Image saved successfully: ${filename}` 
                : `圖片保存成功: ${filename}`,
            'success'
        );
    }
    
    // 顯示上傳警告
    showUploadWarning() {
        this.showToast(
            this.posterMaker.currentLanguage === 'en' 
                ? 'Image loaded locally (server save failed)' 
                : '圖片已本地載入（服務器保存失敗）',
            'warning'
        );
    }
    
    // 顯示提示消息
    showToast(message, type = 'info') {
        const toast = document.createElement('div');
        toast.className = `kms-toast kms-toast-${type}`;
        toast.textContent = message;
        
        document.body.appendChild(toast);
        
        // 顯示動畫
        setTimeout(() => {
            toast.classList.add('kms-toast-show');
        }, 100);
        
        // 自動隱藏
        setTimeout(() => {
            toast.classList.remove('kms-toast-show');
            setTimeout(() => {
                if (toast.parentNode) {
                    toast.parentNode.removeChild(toast);
                }
            }, 300);
        }, 3000);
    }
    
    // 獲取已保存的圖片列表
    async getSavedImages() {
        try {
            const response = await fetch('php/manage_images.php', {
                method: 'GET'
            });
            
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            
            const result = await response.json();
            return result;
            
        } catch (error) {
            console.error('Error fetching saved images:', error);
            throw error;
        }
    }
    
    // 刪除服務器上的圖片
    async deleteServerImage(filename) {
        try {
            const response = await fetch('php/manage_images.php', {
                method: 'DELETE',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ filename: filename })
            });
            
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            
            const result = await response.json();
            return result;
            
        } catch (error) {
            console.error('Error deleting image from server:', error);
            throw error;
        }
    }
    
    // Trading card support methods
    checkAndApplyToTradingCards(imageElement) {
        // Check if trading card mode is active
        const tradingCardSection = document.getElementById('tradingCardLayoutSection');
        if (tradingCardSection && !tradingCardSection.classList.contains('hidden') && window.kmsTradingCard) {
            // Auto-apply image to all trading cards when created
            setTimeout(() => {
                this.applyImageToTradingCards(imageElement);
            }, 100);
        }
    }
    
    applyImageToTradingCards(imageElement) {
        if (!window.kmsTradingCard || !imageElement) return;
        
        const img = imageElement.querySelector('img');
        if (!img || !img.src) return;
        
        window.kmsTradingCard.applyImageToAllCards(img.src);
    }
    
    // Method to manually apply current canvas image to trading cards
    applyCanvasImageToTradingCards() {
        if (!window.kmsTradingCard) return;
        
        const canvas = document.getElementById('posterCanvas');
        const imageElements = canvas.querySelectorAll('.kms-image-element');
        
        if (imageElements.length === 0) {
            window.kmsTradingCard.showMessage('請先在畫布上添加圖片');
            return;
        }
        
        // Apply the first image element or selected image element
        const selectedElement = this.posterMaker.selectedElement;
        const targetElement = (selectedElement && selectedElement.classList.contains('kms-image-element')) 
            ? selectedElement 
            : imageElements[0];
            
        this.applyImageToTradingCards(targetElement);
    }

    initializeRotationButton() {
        const rotateBtn = document.getElementById('rotateImageBtn');
        if (rotateBtn) {
            rotateBtn.addEventListener('click', () => this.rotateSelectedElement());
        }
    }

    rotateSelectedElement() {
        if (!this.posterMaker.selectedElement) return;

        const element = this.posterMaker.selectedElement;

        // 獲取當前旋轉角度
        let currentRotation = 0;
        const transform = element.style.transform;
        const rotateMatch = transform.match(/rotate\((-?\d+)deg\)/);
        if (rotateMatch) {
            currentRotation = parseInt(rotateMatch[1]);
        }

        // 增加 90 度
        const newRotation = (currentRotation + 90) % 360;

        // 應用新的旋轉
        const otherTransforms = transform.replace(/rotate\(-?\d+deg\)\s*/g, '').trim();
        element.style.transform = `${otherTransforms} rotate(${newRotation}deg)`.trim();

        if (this.posterMaker && this.posterMaker.showMessage) {
            this.posterMaker.showMessage(`圖片已旋轉到 ${newRotation}°`);
        }
    }
}
