/**
 * KMS Poster Maker - Main Core Application Controller
 * 主應用程序核心控制器
 */

class KMSPosterMaker {
    constructor() {
        this.currentLanguage = 'en';
        this.currentPaperSize = 'letter';
        this.selectedElement = null;
        this.elements = [];
        this.elementCounter = 0;
        this.isDragging = false;
        this.dragOffset = { x: 0, y: 0 };
        this.layerManager = null;
        this.translations = this.getTranslations();
        this.init();
    }

    init() {
        this.initializeComponents();
        this.setupEventListeners();
        this.loadFonts();
        this.updateLanguage();
        
        // Ensure page is fully loaded before executing scaling
        setTimeout(() => {
            this.canvasControls.handleCanvasResize();
            this.canvasControls.initializeZoomSlider();
        }, 100);
    }

    initializeComponents() {
        this.posterData = new KMSPosterData(this);
        this.printHandler = new KMSPrintCore(this);
        this.imageHandler = new KMSImageHandler(this);
        this.qrGenerator = new KMSQRGenerator(this);
        this.textHandler = new KMSTextHandler(this);
        this.canvasControls = new KMSCanvasControls(this);
        this.quickColors = new KMSQuickColors(this);
        
        // Initialize trading card handler
        if (typeof KMSTradingCard !== 'undefined') {
            this.tradingCard = new KMSTradingCard(this);
            window.kmsTradingCard = this.tradingCard;
        }
        
        // Initialize drag drop handler
        if (typeof KMSDragDropHandler !== 'undefined') {
            this.dragDropHandler = new KMSDragDropHandler(this);
        }
        
        this.initializeLayerManager();
        this.initializeEnhancedControls();
        
        // Setup canvas after components are initialized
        setTimeout(() => {
            this.canvasControls.setupCanvas();
        }, 50);
    }

    setupEventListeners() {
        const eventMappings = [
            { id: 'languageToggle', event: 'click', handler: () => this.toggleLanguage() },
            { id: 'addTextBtn', event: 'click', handler: () => this.addTextElement() },
            { id: 'addImageBtn', event: 'click', handler: () => this.triggerImageUpload() },
            { id: 'addQRBtn', event: 'click', handler: () => this.showQRGenerator() },
            { id: 'printBtn', event: 'click', handler: () => this.printPoster() }
        ];

        eventMappings.forEach(mapping => {
            const element = document.getElementById(mapping.id);
            if (element) {
                element.addEventListener(mapping.event, mapping.handler);
            }
        });

        // Canvas click handler
        const canvas = document.getElementById('posterCanvas');
        if (canvas) {
            canvas.addEventListener('click', (e) => this.handleCanvasClick(e));
        }

        // Keyboard events
        document.addEventListener('keydown', (e) => this.handleKeyDown(e));

        // Drag and drop for canvas container
        const canvasContainer = document.querySelector('.kms-canvas-container');
        if (canvasContainer) {
            canvasContainer.addEventListener('dragover', (e) => e.preventDefault());
            canvasContainer.addEventListener('drop', (e) => {
                e.preventDefault();
                this.handleFileDrop(e);
            });
        }

        // Initialize refresh button
        this.initializeRefreshButton();

        // Initialize 4x6 rotate button
        this.initialize4x6RotateButton();
    }

    toggleLanguage() {
        this.currentLanguage = this.currentLanguage === 'en' ? 'zh' : 'en';
        this.updateLanguage();
    }

    getTranslations() {
        return {
            en: {
                title: 'KMS Poster Maker',
                print: 'Print Poster',
                fileManagement: 'File Management',
                savePoster: 'Save Poster',
                loadPoster: 'Load Poster',
                manageSaved: 'Manage Saved',
                quickColors: 'Quick Colors',
                textColor: 'Text Color',
                textBorder: 'Text Border',
                textBackground: 'Text Background',
                textShadow: 'Text Shadow',
                imageBorder: 'Image Border',
                applyingTo: 'Applying to:',
                paperSize: 'Paper Size',
                letter: 'Letter',
                size4x6: '4" × 6"',
                addElements: 'Add Elements',
                addText: 'Add Text',
                addImage: 'Add Image',
                addQR: 'Add QR Code',
                qrGenerator: 'QR Code Generator',
                size: 'Size',
                foregroundColor: 'Foreground',
                backgroundColor: 'Background',
                borderRadius: 'Radius',
                border: 'Border',
                cells: 'cells',
                textControls: 'Text Controls',
                font: 'Font Family',
                fontSize: 'Font Size',
                fontColor: 'Font Color',
                borderWidth: 'Border Width',
                borderRadius: 'Border Radius',
                background: 'Background',
                canvasControls: 'Canvas Controls',
                toggleGrid: 'Toggle Grid',
                zoomControl: 'Zoom',
                layerControls: 'Layer Controls',
                currentLayer: 'Current Layer:',
                totalLayers: 'Total Layers:',
                bringToFront: 'To Front',
                bringForward: 'Forward',
                sendBackward: 'Backward',
                sendToBack: 'To Back',
                layerList: 'Layer List',
                borders: 'Canvas Borders',
                imageControls: 'Image Controls',
                uploadImage: 'Click to upload or drag image here',
                supportedFormats: 'Supported: JPG, PNG, GIF',
                // File Manager translations
                noPreview: 'No Preview',
                paper: 'Paper',
                elements: 'Elements',
                load: 'Load',
                export: 'Export',
                delete: 'Delete',
                save: 'Save',
                cancel: 'Cancel',
                enterName: 'Enter poster name',
                enterPosterName: 'Enter poster name',
                posterName: 'Poster Name',
                savedPosters: 'Saved Posters',
                managePosters: 'Manage Posters',
                loadPosters: 'Load Posters',
                noSavedPosters: 'No saved posters found',
                confirmDelete: 'Are you sure you want to delete this poster?',
                confirmDeleteAll: 'Are you sure you want to delete all saved posters?',
                // Pagination and search translations
                search: 'Search',
                searchPosters: 'Search posters...',
                previous: 'Previous',
                next: 'Next',
                page: 'Page',
                of: 'of',
                total: 'total',
                noPostersFound: 'No posters found',
                showingResults: 'Showing results',
                deleteAll: 'Delete All',
                posterSaved: 'Poster saved successfully!',
                posterLoaded: 'Poster loaded successfully!',
                posterDeleted: 'Poster deleted successfully!',
                exportSuccess: 'Poster exported successfully!',
                exportJSON: 'Export JSON',
                importFromFile: 'Import from File',
                import: 'Import',
                total: 'Total',
                posters: 'Posters',
                clearAll: 'Clear All',
                close: 'Close',
                noSavedPostersMessage: 'No saved posters found.',
                createPosterMessage: 'Create your first poster to get started!',
                ok: 'OK',
                selectJSONFile: 'Please select a JSON file to import.'
            },
            zh: {
                title: 'KMS 海報製作器',
                print: '打印海報',
                fileManagement: '檔案管理',
                savePoster: '儲存海報',
                loadPoster: '載入海報',
                manageSaved: '管理已儲存',
                quickColors: '快速顏色',
                textColor: '文字顏色',
                textBorder: '文字邊框',
                textBackground: '文字背景',
                textShadow: '文字陰影',
                imageBorder: '圖片邊框',
                applyingTo: '應用到：',
                paperSize: '紙張大小',
                letter: 'Letter',
                size4x6: '4" × 6"',
                addElements: '添加元素',
                addText: '添加文字',
                addImage: '添加圖片',
                addQR: '添加二維碼',
                qrGenerator: 'QR Code 生成器',
                size: '大小',
                foregroundColor: '前景色',
                backgroundColor: '背景色',
                borderRadius: '圓角',
                border: '邊框',
                cells: '格',
                textControls: '文字控制',
                font: '字體',
                fontSize: '字體大小',
                fontColor: '字體顏色',
                borderWidth: '邊框寬度',
                borderRadius: '邊框圓角',
                background: '背景',
                canvasControls: '畫布控制',
                toggleGrid: '切換網格',
                zoomControl: '縮放',
                layerControls: '圖層控制',
                currentLayer: '當前圖層:',
                totalLayers: '總圖層數:',
                bringToFront: '置頂',
                bringForward: '向前',
                sendBackward: '向後',
                sendToBack: '置底',
                layerList: '圖層列表',
                borders: '畫布邊框',
                imageControls: '圖片控制',
                uploadImage: '點擊上傳或拖拽圖片到此處',
                supportedFormats: '支持格式：JPG、PNG、GIF',
                // File Manager translations
                noPreview: '無預覽',
                paper: '紙張',
                elements: '元素',
                load: '載入',
                export: '匯出',
                delete: '刪除',
                save: '儲存',
                cancel: '取消',
                enterName: '輸入海報名稱',
                enterPosterName: '輸入海報名稱',
                posterName: '海報名稱',
                savedPosters: '已儲存海報',
                managePosters: '管理海報',
                loadPosters: '載入海報',
                noSavedPosters: '未找到已儲存的海報',
                confirmDelete: '確定要刪除此海報嗎？',
                confirmDeleteAll: '確定要刪除所有已儲存的海報嗎？',
                // Pagination and search translations
                search: '搜索',
                searchPosters: '搜索海報...',
                previous: '上一頁',
                next: '下一頁',
                page: '第',
                of: '頁，共',
                total: '個',
                noPostersFound: '未找到海報',
                showingResults: '顯示結果',
                deleteAll: '全部刪除',
                posterSaved: '海報儲存成功！',
                posterLoaded: '海報載入成功！',
                posterDeleted: '海報刪除成功！',
                exportSuccess: '海報匯出成功！',
                exportJSON: '匯出 JSON',
                importFromFile: '從檔案匯入',
                import: '匯入',
                total: '總計',
                posters: '海報',
                clearAll: '全部清除',
                close: '關閉',
                noSavedPostersMessage: '未找到已儲存的海報。',
                createPosterMessage: '創建您的第一個海報開始使用！',
                ok: '確定',
                selectJSONFile: '請選擇要匯入的 JSON 檔案。'
            }
        };
    }

    updateLanguage() {
        const t = this.translations[this.currentLanguage];
        
        document.querySelectorAll('[data-translate]').forEach(element => {
            const key = element.dataset.translate;
            if (t[key]) {
                if (element.tagName === 'INPUT' && element.type !== 'button') {
                    element.placeholder = t[key];
                } else {
                    element.textContent = t[key];
                }
            }
        });

        // Update elements with data-en and data-zh attributes
        document.querySelectorAll('[data-en][data-zh]').forEach(element => {
            const text = this.currentLanguage === 'en' ? element.dataset.en : element.dataset.zh;
            if (text) {
                element.textContent = text;
            }
        });

        // Update language toggle button
        const langToggle = document.getElementById('languageToggle');
        if (langToggle) {
            langToggle.textContent = this.currentLanguage === 'en' ? '中文' : 'English';
        }

        // Update quick color display
        if (this.quickColors) {
            this.quickColors.updateQuickColorDisplay();
        }
    }

    loadFonts() {
        // Use local fonts or system fonts to avoid CORS errors
        const fontFamilies = [
            'Arial, sans-serif',
            'Times New Roman, serif',
            'Helvetica, Arial, sans-serif',
            'Georgia, serif',
            'Verdana, sans-serif',
            'Courier New, monospace',
            'Impact, sans-serif',
            'Comic Sans MS, cursive',
            'Trebuchet MS, sans-serif',
            'Palatino, serif'
        ];

        // Test font loading
        fontFamilies.forEach(font => {
            const testElement = document.createElement('div');
            testElement.style.fontFamily = font;
            testElement.style.position = 'absolute';
            testElement.style.left = '-9999px';
            testElement.textContent = 'Test';
            document.body.appendChild(testElement);
            
            setTimeout(() => {
                document.body.removeChild(testElement);
            }, 100);
        });
    }

    addTextElement() {
        if (this.textHandler) {
            const textElement = this.textHandler.createTextElement();
            if (textElement) {
                // 檢查是否在 trading card 模式下
                const isInTradingCard = textElement.classList.contains('kms-trading-card-text-element');

                if (!isInTradingCard) {
                    // 只有在非 trading card 模式下才添加到畫布
                    this.addElementToCanvas(textElement);
                }

                this.selectElement(textElement);

                // Focus on the text element for immediate editing
                setTimeout(() => {
                    textElement.focus();
                }, 100);
            }
        }
    }

    setupElementInteraction(element) {
        // Use drag drop handler if available
        if (this.dragDropHandler) {
            element.addEventListener('mousedown', (e) => {
                // Check if we should start dragging
                if (this.dragDropHandler.startDrag(e, element)) {
                    // Drag started successfully
                    return;
                }
                // Fallback to old drag system
                this.startDrag(e, element);
            });
        } else {
            // Fallback to old drag system
            element.addEventListener('mousedown', (e) => this.startDrag(e, element));
        }
        
        element.addEventListener('click', (e) => {
            e.stopPropagation();
            this.selectElement(element);
        });
    }

    startDrag(e, element) {
        if (this.dragDropHandler) {
            return this.dragDropHandler.startDrag(e, element);
        }
        
        // Fallback drag implementation
        this.isDragging = true;
        this.selectedElement = element;
        
        const rect = element.getBoundingClientRect();
        const canvasRect = document.getElementById('posterCanvas').getBoundingClientRect();
        
        this.dragOffset = {
            x: e.clientX - rect.left,
            y: e.clientY - rect.top
        };
        
        document.addEventListener('mousemove', (e) => this.handleDrag(e));
        document.addEventListener('mouseup', () => this.stopDrag());
        
        e.preventDefault();
    }

    handleDrag(e) {
        if (!this.isDragging || !this.selectedElement) return;
        
        const canvas = document.getElementById('posterCanvas');
        const canvasRect = canvas.getBoundingClientRect();
        
        const x = e.clientX - canvasRect.left - this.dragOffset.x;
        const y = e.clientY - canvasRect.top - this.dragOffset.y;
        
        // Keep element within canvas bounds
        const maxX = canvas.offsetWidth - this.selectedElement.offsetWidth;
        const maxY = canvas.offsetHeight - this.selectedElement.offsetHeight;
        
        this.selectedElement.style.left = Math.max(0, Math.min(x, maxX)) + 'px';
        this.selectedElement.style.top = Math.max(0, Math.min(y, maxY)) + 'px';
    }

    stopDrag() {
        this.isDragging = false;
        document.removeEventListener('mousemove', this.handleDrag);
        document.removeEventListener('mouseup', this.stopDrag);
    }

    selectElement(element) {
        // 檢查element是否為null或undefined
        if (!element) {
            console.warn('selectElement: element is null or undefined');
            return;
        }

        // 移除所有元素的選中狀態（包括 trading card 元素）
        document.querySelectorAll('.kms-canvas-element, .kms-trading-card-text-element, .kms-trading-card-image-element, .kms-trading-card-qr-element').forEach(el => {
            el.classList.remove('selected');
        });

        element.classList.add('selected');
        this.selectedElement = element;
        this.updateControlsForElement(element);

        if (this.layerManager) {
            this.layerManager.updateLayerControls();
            this.layerManager.updateLayerList();
        }
    }

    hideAllControls() {
        const sections = ['textControlsSection', 'imageControlsSection', 'qrControlsSection'];
        sections.forEach(sectionId => {
            const section = document.getElementById(sectionId);
            if (section) {
                section.style.display = 'none';
            }
        });
    }

    updateControlsForElement(element) {
        this.hideAllControls();
        
        const rightLayerControlsSection = document.getElementById('rightLayerControlsSection');
        if (rightLayerControlsSection) {
            rightLayerControlsSection.style.display = 'block';
        }

        const elementTypeConfigs = {
            'kms-text-element': {
                section: 'textControlsSection',
                handler: this.textHandler,
                updateMethod: 'updateControlsForTextElement'
            },
            'kms-trading-card-text-element': {
                section: 'textControlsSection',
                handler: this.textHandler,
                updateMethod: 'updateControlsForTextElement'
            },
            'kms-image-element': {
                section: 'imageControlsSection',
                handler: this.imageHandler,
                updateMethod: 'updateControlsForImageElement'
            },
            'kms-trading-card-image-element': {
                section: 'imageControlsSection',
                handler: this.imageHandler,
                updateMethod: 'updateControlsForImageElement'
            },
            'kms-qr-element': {
                section: 'qrControlsSection',
                handler: this.qrGenerator,
                updateMethod: 'updateControlsForQRElement'
            },
            'kms-trading-card-qr-element': {
                section: 'qrControlsSection',
                handler: this.qrGenerator,
                updateMethod: 'updateControlsForQRElement'
            }
        };

        let activeSectionId = null;

        Object.keys(elementTypeConfigs).forEach(className => {
            if (element.classList.contains(className)) {
                const config = elementTypeConfigs[className];
                const section = document.getElementById(config.section);
                if (section) {
                    section.style.display = 'block';
                    activeSectionId = config.section;
                }
                if (config.handler && config.updateMethod && config.handler[config.updateMethod]) {
                    config.handler[config.updateMethod](element);
                }
            }
        });

        // 自動滾動到顯示的控制面板
        if (activeSectionId) {
            setTimeout(() => {
                const activeSection = document.getElementById(activeSectionId);
                if (activeSection) {
                    activeSection.scrollIntoView({ 
                        behavior: 'smooth', 
                        block: 'start',
                        inline: 'nearest'
                    });
                }
            }, 100);
        }
    }

    handleCanvasClick(e) {
        if (e.target.id === 'posterCanvas') {
            this.selectedElement = null;
            // 使用與 selectElement 方法一致的選擇器
            document.querySelectorAll('.kms-canvas-element, .kms-trading-card-text-element, .kms-trading-card-image-element, .kms-trading-card-qr-element').forEach(el => {
                el.classList.remove('selected');
            });

            if (this.layerManager) {
                this.layerManager.updateLayerControls();
                this.layerManager.updateLayerList();
            }
            this.hideAllControls();
        }
    }

    handleKeyDown(e) {
        if (e.key === 'Delete' && this.selectedElement) {
            this.deleteSelectedElement();
        }
    }

    deleteSelectedElement() {
        if (this.selectedElement) {
            const elementToRemove = this.selectedElement;
            this.elements = this.elements.filter(el => el !== elementToRemove);

            if (this.layerManager) {
                this.layerManager.onElementRemoved(elementToRemove);
            }
            
            elementToRemove.remove();
            this.selectedElement = null;
            this.hideAllControls();
        }
    }

    triggerImageUpload() {
        if (this.imageHandler) {
            const input = document.createElement('input');
            input.type = 'file';
            input.accept = 'image/*';
            input.onchange = (e) => {
                if (e.target.files[0]) {
                    this.imageHandler.handleImageUpload(e.target.files[0]);
                }
            };
            input.click();
        }
    }

    showQRGenerator() {
        if (this.qrGenerator) {
            const qrControlsSection = document.getElementById('qrControlsSection');
            if (qrControlsSection) {
                qrControlsSection.style.display = 'block';
                qrControlsSection.classList.remove('kms-hidden-section');
            }
            
            // Hide other control sections
            this.hideAllControls();
            if (qrControlsSection) {
                qrControlsSection.style.display = 'block';
                qrControlsSection.classList.remove('kms-hidden-section');
            }
        }
    }

    printPoster() {
        if (this.printHandler) {
            this.printHandler.printPoster();
        }
    }

    handleFileDrop(e) {
        const files = Array.from(e.dataTransfer.files);
        const imageFiles = files.filter(file => file.type.startsWith('image/'));

        if (imageFiles.length > 0 && this.imageHandler) {
            imageFiles.forEach(file => {
                this.imageHandler.handleImageUpload(file);
            });
        }
    }

    initializeRefreshButton() {
        const refreshBtn = document.getElementById('refreshPageBtn');
        if (refreshBtn) {
            refreshBtn.addEventListener('click', () => this.refreshPage());
        }
    }

    initialize4x6RotateButton() {
        const rotateBtn = document.getElementById('rotate4x6Btn');
        if (rotateBtn) {
            rotateBtn.addEventListener('click', () => this.rotate4x6Canvas());
        }
    }

    async refreshPage() {
        try {
            // 清除資料庫和檔案
            const response = await fetch('php/clear_all_data.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                }
            });

            // 檢查響應是否為JSON
            const contentType = response.headers.get('content-type');
            if (!contentType || !contentType.includes('application/json')) {
                const text = await response.text();
                console.error('Server returned non-JSON response:', text);
                throw new Error('服務器返回了無效的響應格式');
            }

            const result = await response.json();

            if (response.ok && result.status === 200) {
                // 清除成功，重新載入頁面
                window.location.reload();
            } else {
                console.error('清除資料時發生錯誤：', result.message);
                // 即使有錯誤也重新載入頁面
                window.location.reload();
            }
        } catch (error) {
            console.error('清除資料時發生錯誤：', error);
            // 即使有錯誤也重新載入頁面
            window.location.reload();
        }
    }

    rotate4x6Canvas() {
        // 切換 4x6 畫布方向
        const canvas = document.getElementById('posterCanvas');
        if (!canvas) return;

        const isCurrentlyLandscape = canvas.classList.contains('kms-canvas-4x6-landscape');

        if (isCurrentlyLandscape) {
            // 切換到直向 (384px x 576px)
            canvas.classList.remove('kms-canvas-4x6-landscape');
            canvas.classList.add('kms-canvas-4x6-portrait');
            canvas.style.width = '384px';
            canvas.style.height = '576px';
        } else {
            // 切換到橫向 (576px x 384px) - 這是4x6的標準方向
            canvas.classList.remove('kms-canvas-4x6', 'kms-canvas-4x6-portrait');
            canvas.classList.add('kms-canvas-4x6-landscape');
            canvas.style.width = '576px';
            canvas.style.height = '384px';
        }

        // 更新按鈕文字
        const rotateBtn = document.getElementById('rotate4x6Btn');
        if (rotateBtn) {
            const span = rotateBtn.querySelector('span');
            if (span) {
                span.textContent = isCurrentlyLandscape ? '橫向' : '直向';
            }
        }

        this.showMessage(isCurrentlyLandscape ? '已切換到直向模式' : '已切換到橫向模式');
    }

    showMessage(message, type = 'success') {
        // 創建消息元素
        const messageDiv = document.createElement('div');
        messageDiv.className = `kms-message kms-message-${type}`;
        messageDiv.textContent = message;
        messageDiv.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: ${type === 'success' ? '#4CAF50' : '#f44336'};
            color: white;
            padding: 12px 20px;
            border-radius: 4px;
            z-index: 10000;
            font-size: 14px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.2);
            transition: opacity 0.3s ease;
        `;

        document.body.appendChild(messageDiv);

        // 3秒後自動移除
        setTimeout(() => {
            messageDiv.style.opacity = '0';
            setTimeout(() => {
                if (messageDiv.parentNode) {
                    messageDiv.parentNode.removeChild(messageDiv);
                }
            }, 300);
        }, 3000);
    }

    initializeLayerManager() {
        if (typeof KMSLayerManager !== 'undefined') {
            this.layerManager = new KMSLayerManager(this);
        }
    }

    addElementToCanvas(element) {
        const canvas = document.getElementById('posterCanvas');
        if (canvas && element) {
            canvas.appendChild(element);
            
            // 檢查元素是否已經在陣列中，避免重複添加
            if (!this.elements.includes(element)) {
                this.elements.push(element);
            }
        }

        if (this.layerManager) {
            this.layerManager.onElementAdded(element);
        }
    }

    getAllElements() {
        return this.elements;
    }

    reorganizeElementLayers() {
        if (this.layerManager) {
            this.layerManager.reorganizeZIndexes();
        }
    }

    initializeEnhancedControls() {
        this.initializeRangeDisplays();
        this.initializeColorValueDisplays();
    }

    initializeRangeDisplays() {
        document.querySelectorAll('.kms-enhanced-range').forEach(range => {
            range.addEventListener('input', (e) => {
                const display = document.getElementById(range.id + 'Value');
                if (display) {
                    const unit = range.id.includes('Opacity') ? '%' : 'px';
                    display.textContent = e.target.value + unit;
                }
            });
        });
    }

    initializeColorValueDisplays() {
        document.querySelectorAll('.kms-enhanced-color-picker').forEach(picker => {
            picker.addEventListener('input', (e) => {
                const colorRow = picker.closest('.kms-enhanced-color-row');
                if (colorRow) {
                    const colorValue = colorRow.querySelector('.kms-color-value');
                    if (colorValue) {
                        colorValue.textContent = picker.value.toUpperCase();
                    }
                }
            });
        });
    }

    updateCanvasLayout() {
        if (this.canvasControls) {
            this.canvasControls.handleCanvasResize();
        }
    }

    // Delegate methods to appropriate modules
    changePaperSize(size) {
        if (this.canvasControls) {
            this.canvasControls.changePaperSize(size);
        }
    }

    setCanvasZoom(scale) {
        if (this.canvasControls) {
            this.canvasControls.setCanvasZoom(scale);
        }
    }

    savePosterData(posterName) {
        if (this.printHandler && this.printHandler.posterData) {
            return this.printHandler.posterData.savePosterData(posterName);
        }
    }

    loadPosterData(posterData) {
        if (this.printHandler && this.printHandler.posterData) {
            return this.printHandler.posterData.loadPosterData(posterData);
        }
    }
}

// Initialize the main application when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.kmsPosterMaker = new KMSPosterMaker();
});
