<?php
/**
 * KMS Poster Maker - Image Upload API
 * 圖片上傳 API
 */

require_once 'config.php';

// 處理 CORS 預檢請求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    header('Access-Control-Allow-Origin: *');
    header('Access-Control-Allow-Methods: POST, OPTIONS');
    header('Access-Control-Allow-Headers: Content-Type');
    exit;
}

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    sendError('不允許的請求方法', 405);
}

class ImageUploadAPI {
    private $db;
    
    public function __construct() {
        $this->db = Database::getInstance()->getConnection();
    }
    
    /**
     * 處理圖片上傳
     */
    public function handleUpload() {
        try {
            // 添加請求調試信息
            error_log('=== 開始處理圖片上傳 ===');
            error_log('REQUEST_METHOD: ' . $_SERVER['REQUEST_METHOD']);
            error_log('Content-Type: ' . ($_SERVER['CONTENT_TYPE'] ?? 'not set'));
            error_log('$_FILES: ' . print_r($_FILES, true));
            error_log('$_POST: ' . print_r($_POST, true));
            
            // 檢查是否有檔案上傳
            if (!isset($_FILES['image'])) {
                error_log('錯誤: 未提供圖片檔案');
                sendError('未提供圖片檔案', 400);
                return;
            }
            
            $file = $_FILES['image'];
            
            // 檢查檔案是否有效
            if (!is_array($file)) {
                error_log('錯誤: 檔案數據不是陣列格式');
                sendError('檔案數據不是陣列格式', 400);
                return;
            }
            
            if (empty($file['name'])) {
                error_log('錯誤: 檔案名稱為空');
                sendError('檔案名稱為空', 400);
                return;
            }
            
            // 添加調試信息
            error_log('Upload file info: ' . print_r($file, true));
            
            $posterId = $_POST['posterId'] ?? null;
            $elementId = $_POST['elementId'] ?? null;
            
            // 檢查臨時檔案是否存在
            if (empty($file['tmp_name'])) {
                error_log('錯誤: 臨時檔案路徑為空 - tmp_name: ' . ($file['tmp_name'] ?? 'null'));
                error_log('檔案錯誤代碼: ' . $file['error']);
                error_log('PHP 上傳設定: upload_max_filesize=' . ini_get('upload_max_filesize') . ', post_max_size=' . ini_get('post_max_size'));
                error_log('檔案大小: ' . ($file['size'] ?? 'unknown'));

                // 提供更詳細的錯誤信息
                $errorMsg = '檔案上傳失敗：';
                switch ($file['error']) {
                    case UPLOAD_ERR_INI_SIZE:
                        $errorMsg .= '檔案大小超過 PHP 設定限制 (upload_max_filesize)';
                        break;
                    case UPLOAD_ERR_FORM_SIZE:
                        $errorMsg .= '檔案大小超過表單設定限制';
                        break;
                    case UPLOAD_ERR_PARTIAL:
                        $errorMsg .= '檔案只有部分被上傳';
                        break;
                    case UPLOAD_ERR_NO_FILE:
                        $errorMsg .= '沒有檔案被上傳';
                        break;
                    case UPLOAD_ERR_NO_TMP_DIR:
                        $errorMsg .= '找不到暫存目錄';
                        break;
                    case UPLOAD_ERR_CANT_WRITE:
                        $errorMsg .= '檔案寫入失敗';
                        break;
                    case UPLOAD_ERR_EXTENSION:
                        $errorMsg .= 'PHP 擴展停止了檔案上傳';
                        break;
                    default:
                        $errorMsg .= '臨時檔案不存在，可能是檔案太大或上傳過程中斷';
                }

                sendError($errorMsg, 400);
                return;
            }
            
            // 驗證檔案
            $this->validateFile($file);
            
            // 生成唯一檔案名
            $filename = generateUniqueFilename($file['name']);
            $filepath = IMAGE_UPLOAD_PATH . $filename;
            
            // 移動檔案
            if (!move_uploaded_file($file['tmp_name'], $filepath)) {
                sendError('檔案移動失敗：無法將檔案保存到目標位置', 500);
                return;
            }
            
            // 取得圖片資訊
            $imageInfo = getimagesize($filepath);
            $width = $imageInfo[0] ?? 0;
            $height = $imageInfo[1] ?? 0;
            $mimeType = $imageInfo['mime'] ?? $file['type'];
            
            // 儲存到資料庫
            $imageId = $this->saveImageRecord($posterId, $elementId, $file['name'], $filename, $filepath, $file['size'], $mimeType, $width, $height);
            
            // 回傳成功結果
            sendResponse([
                'id' => $imageId,
                'filename' => $filename,
                'originalName' => $file['name'],
                'url' => 'uploads/images/' . $filename,
                'size' => $file['size'],
                'sizeFormatted' => formatFileSize($file['size']),
                'width' => $width,
                'height' => $height,
                'mimeType' => $mimeType,
                'isServerImage' => true
            ], 200, '圖片上傳成功');
            
        } catch (Exception $e) {
            // 清理已上傳的檔案
            if (isset($filepath) && file_exists($filepath)) {
                unlink($filepath);
            }
            sendError('上傳錯誤: ' . $e->getMessage(), 500);
        }
    }
    
    /**
     * 驗證檔案
     */
    private function validateFile($file) {
        // 添加調試信息
        error_log('Validating file with error code: ' . $file['error']);
        
        // 檢查上傳錯誤 - 忽略檔案大小限制錯誤
        if ($file['error'] !== UPLOAD_ERR_OK && 
            $file['error'] !== UPLOAD_ERR_INI_SIZE && 
            $file['error'] !== UPLOAD_ERR_FORM_SIZE) {
            
            $errorMessages = [
                UPLOAD_ERR_PARTIAL => '檔案只有部分被上傳',
                UPLOAD_ERR_NO_FILE => '沒有檔案被上傳',
                UPLOAD_ERR_NO_TMP_DIR => '找不到暫存目錄',
                UPLOAD_ERR_CANT_WRITE => '檔案寫入失敗',
                UPLOAD_ERR_EXTENSION => 'PHP 擴展停止了檔案上傳'
            ];
            
            $message = $errorMessages[$file['error']] ?? '未知的上傳錯誤 (代碼: ' . $file['error'] . ')';
            throw new Exception($message);
        }
        
        // 如果是檔案大小錯誤，記錄但不拋出異常
        if ($file['error'] === UPLOAD_ERR_INI_SIZE || $file['error'] === UPLOAD_ERR_FORM_SIZE) {
            error_log('檔案大小超過限制，但繼續處理: ' . $file['name']);
        }
        
        // 驗證檔案類型
        if (!validateImageType($file['name'])) {
            throw new Exception('不支援的檔案類型。只允許: ' . implode(', ', ALLOWED_IMAGE_TYPES));
        }
        
        // 驗證 MIME 類型 - 更寬鬆的檢查
        $allowedMimeTypes = [
            'image/jpeg',
            'image/jpg', 
            'image/png',
            'image/gif',
            'image/webp',
            'image/svg+xml',
            'application/octet-stream', // 某些情況下的通用類型
            'text/plain' // SVG有時會被識別為text/plain
        ];
        
        // 獲取檔案擴展名
        $fileExtension = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));
        
        // 如果是支援的圖片擴展名，則跳過MIME類型檢查
        $supportedExtensions = ['jpg', 'jpeg', 'png', 'gif', 'webp', 'svg'];
        if (!in_array($fileExtension, $supportedExtensions)) {
            throw new Exception('不支援的檔案擴展名: ' . $fileExtension);
        }
        
        // 只在MIME類型明顯錯誤時才拋出錯誤
        if (!empty($file['type']) && !in_array($file['type'], $allowedMimeTypes) && !str_starts_with($file['type'], 'image/')) {
            throw new Exception('無效的 MIME 類型: ' . $file['type']);
        }
        
        // 移除檔案大小限制檢查，讓PHP自己處理
        
        // 檢查臨時檔案是否可用（已在handleUpload中檢查過是否為空）
        if (!empty($file['tmp_name']) && !file_exists($file['tmp_name'])) {
            throw new Exception('臨時檔案不存在: ' . $file['tmp_name']);
        }
        
        if (!empty($file['tmp_name']) && !is_readable($file['tmp_name'])) {
            throw new Exception('臨時檔案無法讀取: ' . $file['tmp_name']);
        }
        
        // 驗證檔案內容（防止偽造）- 對SVG檔案跳過此檢查
        if ($fileExtension !== 'svg' && !empty($file['tmp_name']) && file_exists($file['tmp_name'])) {
            $imageInfo = getimagesize($file['tmp_name']);
            if ($imageInfo === false) {
                throw new Exception('無效的圖片檔案');
            }
        }
    }
    
    /**
     * 儲存圖片記錄到資料庫
     */
    private function saveImageRecord($posterId, $elementId, $originalName, $filename, $filepath, $fileSize, $mimeType, $width, $height) {
        try {
            $stmt = $this->db->prepare("
                INSERT INTO poster_images 
                (poster_id, element_id, original_filename, server_filename, file_path, file_size, mime_type, width, height) 
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
            ");
            
            $stmt->execute([
                $posterId,
                $elementId,
                $originalName,
                $filename,
                $filepath,
                $fileSize,
                $mimeType,
                $width,
                $height
            ]);
            
            return $this->db->lastInsertId();
            
        } catch (PDOException $e) {
            throw new Exception('資料庫儲存失敗: ' . $e->getMessage());
        }
    }
    
    /**
     * 取得圖片資訊
     */
    public function getImageInfo() {
        $imageId = $_GET['id'] ?? '';
        
        if (!$imageId) {
            sendError('缺少圖片ID', 400);
        }
        
        try {
            $stmt = $this->db->prepare("SELECT * FROM poster_images WHERE id = ?");
            $stmt->execute([$imageId]);
            $image = $stmt->fetch();
            
            if (!$image) {
                sendError('找不到指定的圖片', 404);
            }
            
            $image['size_formatted'] = formatFileSize($image['file_size']);
            $image['url'] = 'uploads/images/' . $image['server_filename'];
            
            sendResponse($image);
            
        } catch (PDOException $e) {
            sendError('資料庫錯誤: ' . $e->getMessage(), 500);
        }
    }
    
    /**
     * 刪除圖片
     */
    public function deleteImage() {
        $imageId = $_GET['id'] ?? '';
        
        if (!$imageId) {
            sendError('缺少圖片ID', 400);
        }
        
        try {
            // 取得圖片資訊
            $stmt = $this->db->prepare("SELECT * FROM poster_images WHERE id = ?");
            $stmt->execute([$imageId]);
            $image = $stmt->fetch();
            
            if (!$image) {
                sendError('找不到指定的圖片', 404);
            }
            
            // 刪除檔案
            if (file_exists($image['file_path'])) {
                unlink($image['file_path']);
            }
            
            // 刪除資料庫記錄
            $stmt = $this->db->prepare("DELETE FROM poster_images WHERE id = ?");
            $stmt->execute([$imageId]);
            
            sendResponse(['deleted' => true], 200, '圖片刪除成功');
            
        } catch (PDOException $e) {
            sendError('資料庫錯誤: ' . $e->getMessage(), 500);
        }
    }
}

// 處理請求
$api = new ImageUploadAPI();
$action = $_GET['action'] ?? 'upload';

switch ($action) {
    case 'upload':
        $api->handleUpload();
        break;
    case 'info':
        $api->getImageInfo();
        break;
    case 'delete':
        $api->deleteImage();
        break;
    default:
        sendError('無效的操作', 400);
}
?>