# KMS Poster Maker - Apache Configuration
# 設定 PHP 上傳限制

# 增加上傳檔案大小限制
php_value upload_max_filesize 500M
php_value post_max_size 500M
php_value max_input_time 300
php_value max_execution_time 300
php_value memory_limit 512M

# 啟用檔案上傳
php_value file_uploads On

# 設定臨時檔案目錄權限
php_value upload_tmp_dir /tmp

# 錯誤報告設定
php_value display_errors On
php_value log_errors On

# 安全設定
<Files "*.php">
    Order allow,deny
    Allow from all
</Files>

# 防止直接訪問敏感檔案
<Files "config.php">
    Order deny,allow
    Deny from all
    Allow from 127.0.0.1
    Allow from ::1
</Files>

# 設定 MIME 類型
AddType image/webp .webp
AddType image/svg+xml .svg

# 啟用 CORS（如果需要）
Header always set Access-Control-Allow-Origin "*"
Header always set Access-Control-Allow-Methods "GET, POST, PUT, DELETE, OPTIONS"
Header always set Access-Control-Allow-Headers "Content-Type, Authorization"
