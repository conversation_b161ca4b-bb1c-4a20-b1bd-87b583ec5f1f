/**
 * K<PERSON> Poster Maker - Poster Data Management (Server-side Storage)
 * 海報數據管理模塊（伺服器端儲存）
 */

class KMSPosterData {
    constructor(posterMaker) {
        this.posterMaker = posterMaker;
        this.apiBaseUrl = 'php/poster_api.php';
        this.imageApiUrl = 'php/upload_image.php';
        this.currentUserId = 1; // 暫時使用預設用戶ID
        this.compressionEnabled = false; // 伺服器端不需要壓縮
    }
    
    /**
     * 取得儲存使用統計
     */
    async getStorageUsage() {
        try {
            const response = await fetch(`${this.apiBaseUrl}?action=storage_stats&userId=${this.currentUserId}`);
            const result = await response.json();
            
            if (result.status === 200) {
                return {
                    bytes: result.data.total_size,
                    mb: result.data.total_size_formatted,
                    posterCount: result.data.poster_count,
                    imageCount: result.data.image_count,
                    isServerStorage: true
                };
            } else {
                throw new Error(result.message || 'Unknown error occurred');
            }
        } catch (error) {
            console.error('Error getting storage usage:', error);
            return { 
                bytes: 0, 
                mb: '0 B', 
                posterCount: 0, 
                imageCount: 0, 
                isServerStorage: true 
            };
        }
    }
    
    /**
     * 儲存海報資料到伺服器
     */
    async savePosterData(posterName = null) {
        try {
            const canvas = document.getElementById('posterCanvas');
            const posterData = {
                id: Date.now().toString() + '_' + Math.random().toString(36).substr(2, 9),
                name: posterName || `Poster_${new Date().toLocaleDateString().replace(/\//g, '-')}`,
                paperSize: this.posterMaker.currentPaperSize,
                backgroundColor: canvas.style.backgroundColor || '#ffffff',
                elements: await this.processElementsForSaving(),
                timestamp: new Date().toISOString(),
                version: '2.0' // 新版本標記為伺服器儲存
            };
            
            // 發送到伺服器
            const response = await fetch(this.apiBaseUrl, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    posterData: posterData,
                    userId: this.currentUserId
                })
            });
            
            const result = await response.json();
            
            if (result.status === 200) {
                this.showSaveSuccessMessage(posterData.name, result.data.fileSize);
                return posterData;
            } else {
                throw new Error(result.message);
            }
            
        } catch (error) {
            console.error('Error saving poster:', error);
            this.showToast(
                this.posterMaker.currentLanguage === 'en' 
                    ? `Save failed: ${error.message}` 
                    : `儲存失敗：${error.message}`,
                'error'
            );
            throw error;
        }
    }
    
    /**
     * 處理元素資料以供儲存
     */
    async processElementsForSaving() {
        const elements = [];
        
        for (const element of this.posterMaker.elements) {
            const elementData = {
                id: element.dataset.elementId || this.generateElementId(),
                type: element.classList.contains('kms-text-element') ? 'text' : 
                      element.classList.contains('kms-image-element') ? 'image' : 'qr',
                position: {
                    x: parseInt(element.style.left) || 0,
                    y: parseInt(element.style.top) || 0
                },
                size: {
                    width: parseInt(element.style.width) || element.offsetWidth,
                    height: parseInt(element.style.height) || element.offsetHeight
                },
                zIndex: parseInt(element.style.zIndex) || 1,
                styles: this.extractElementStyles(element)
            };
            
            // 處理不同類型的元素內容
            if (elementData.type === 'text') {
                elementData.content = element.textContent || element.innerText;
            } else if (elementData.type === 'image') {
                const img = element.querySelector('img');
                if (img) {
                    // 如果是 base64 圖片，上傳到伺服器
                    if (img.src.startsWith('data:image/')) {
                        const uploadResult = await this.uploadBase64Image(img.src, elementData.id);
                        if (uploadResult) {
                            elementData.content = uploadResult.url;
                            elementData.serverFileName = uploadResult.filename;
                            elementData.serverPath = uploadResult.url;
                            elementData.isServerImage = true;
                        } else {
                            elementData.content = img.src; // 保留原始 base64
                        }
                    } else {
                        elementData.content = img.src;
                        elementData.serverFileName = element.dataset.serverFileName || '';
                        elementData.serverPath = element.dataset.serverPath || '';
                        elementData.isServerImage = element.dataset.isServerImage === 'true';
                    }
                    elementData.alt = img.alt || '';
                }
            } else if (elementData.type === 'qr') {
                elementData.content = element.dataset.qrUrl || '';
                elementData.qrSettings = {
                    size: element.dataset.qrSize || '90',
                    foreground: element.dataset.qrForeground || '#00ffff',
                    background: element.dataset.qrBackground || '#000000',
                    radius: element.dataset.qrRadius || '16',
                    border: element.dataset.qrBorder || '1'
                };
            }
            
            elements.push(elementData);
        }
        
        return elements.sort((a, b) => (parseInt(b.zIndex) || 1) - (parseInt(a.zIndex) || 1));
    }
    
    /**
     * 提取元素樣式
     */
    extractElementStyles(element) {
        return {
            fontSize: element.style.fontSize,
            fontFamily: element.style.fontFamily,
            color: element.style.color,
            backgroundColor: element.style.backgroundColor,
            textAlign: element.style.textAlign,
            fontWeight: element.style.fontWeight,
            fontStyle: element.style.fontStyle,
            textDecoration: element.style.textDecoration,
            borderRadius: element.style.borderRadius,
            border: element.style.border,
            padding: element.style.padding,
            lineHeight: element.style.lineHeight,
            textShadow: element.style.textShadow,
            transform: element.style.transform,
            opacity: element.style.opacity,
            boxShadow: element.style.boxShadow
        };
    }
    
    /**
     * 上傳 base64 圖片到伺服器
     */
    async uploadBase64Image(base64Data, elementId) {
        try {
            // 將 base64 轉換為 Blob
            const response = await fetch(base64Data);
            const blob = await response.blob();
            
            // 創建 FormData
            const formData = new FormData();
            formData.append('image', blob, `element_${elementId}.png`);
            formData.append('elementId', elementId);
            
            // 上傳到伺服器
            const uploadResponse = await fetch(this.imageApiUrl, {
                method: 'POST',
                body: formData
            });
            
            const result = await uploadResponse.json();
            
            if (result.status === 200) {
                return result.data;
            } else {
                console.error('Image upload failed:', result.message);
                return null;
            }
            
        } catch (error) {
            console.error('Error uploading base64 image:', error);
            return null;
        }
    }
    
    /**
     * 從伺服器載入海報資料
     */
    async loadPosterData(posterData) {
        try {
            // 如果傳入的是海報ID，從伺服器取得資料
            if (typeof posterData === 'string') {
                const response = await fetch(`${this.apiBaseUrl}?action=get&id=${posterData}`);
                const result = await response.json();
                
                if (result.status === 200) {
                    posterData = result.data.poster_data;
                } else {
                    throw new Error(result.message);
                }
            }
            
            // 清除目前的海報
            this.posterMaker.elements.forEach(element => element.remove());
            this.posterMaker.elements = [];
            this.posterMaker.selectedElement = null;
            
            // 隱藏所有控制區塊
            this.hideAllControlSections();
            
            // 設定紙張大小
            this.posterMaker.changePaperSize(posterData.paperSize || 'letter');
            
            // 設定背景顏色
            const canvas = document.getElementById('posterCanvas');
            canvas.style.backgroundColor = posterData.backgroundColor || '#ffffff';
            
            // 重新建立元素（按 z-index 順序）
            if (posterData.elements && Array.isArray(posterData.elements)) {
                const sortedElements = posterData.elements.slice().sort((a, b) => (a.zIndex || 1) - (b.zIndex || 1));
                for (const elementData of sortedElements) {
                    await this.recreateElement(elementData);
                }
            }
            
            // 更新圖層管理器
            if (this.posterMaker.layerManager) {
                this.posterMaker.layerManager.updateLayerControls();
                this.posterMaker.layerManager.updateLayerList();
            }
            
            this.showLoadSuccessMessage(posterData.name);
            
        } catch (error) {
            console.error('Error loading poster data:', error);
            alert(this.posterMaker.currentLanguage === 'en' 
                ? `Error loading poster: ${error.message}`
                : `載入海報時出錯：${error.message}`);
        }
    }
    
    /**
     * 隱藏所有控制區塊
     */
    hideAllControlSections() {
        const sections = [
            'textControlsSection',
            'imageControlsSection', 
            'qrControlsSection',
            'layerControlsSection'
        ];
        
        sections.forEach(sectionId => {
            const section = document.getElementById(sectionId);
            if (section) section.style.display = 'none';
        });
    }
    
    /**
     * 取得已儲存的海報列表
     */
    async getSavedPosters(page = 1, limit = 20) {
        try {
            const response = await fetch(`${this.apiBaseUrl}?action=list&userId=${this.currentUserId}&page=${page}&limit=${limit}`);
            const result = await response.json();
            
            if (result.status === 200) {
                return result.data;
            } else {
                throw new Error(result.message);
            }
        } catch (error) {
            console.error('Error getting saved posters:', error);
            return { posters: [], pagination: { page: 1, limit: 20, total: 0, totalPages: 0 } };
        }
    }

    /**
     * 取得已儲存的海報列表（同步版本，用於向後兼容）
     */
    getSavedPostersSync() {
        // 使用快取的資料或回傳空陣列
        if (this._cachedPosters) {
            return this._cachedPosters;
        }
        
        // 如果沒有快取，嘗試從 localStorage 取得（向後兼容）
        try {
            const saved = localStorage.getItem('kms_saved_posters');
            return saved ? JSON.parse(saved) : [];
        } catch (error) {
            console.error('Error getting cached posters:', error);
            return [];
        }
    }

    /**
     * 更新快取的海報資料
     */
    async updateCachedPosters() {
        try {
            const result = await this.getSavedPosters();
            this._cachedPosters = result.posters || [];
            return this._cachedPosters;
        } catch (error) {
            console.error('Error updating cached posters:', error);
            return [];
        }
    }
    
    /**
     * 刪除已儲存的海報
     */
    async deleteSavedPoster(posterId) {
        try {
            const response = await fetch(`${this.apiBaseUrl}?action=delete&id=${posterId}`, {
                method: 'DELETE'
            });
            
            const result = await response.json();
            
            if (result.status === 200) {
                this.showToast(
                    this.posterMaker.currentLanguage === 'en' 
                        ? 'Poster deleted successfully' 
                        : '海報刪除成功',
                    'success'
                );
                return true;
            } else {
                throw new Error(result.message);
            }
        } catch (error) {
            console.error('Error deleting poster:', error);
            this.showToast(
                this.posterMaker.currentLanguage === 'en' 
                    ? `Delete failed: ${error.message}` 
                    : `刪除失敗：${error.message}`,
                'error'
            );
            return false;
        }
    }
    
    /**
     * 顯示儲存管理器
     */
    async showStorageManager() {
        const storageUsage = await this.getStorageUsage();
        const savedPosters = await this.getSavedPosters();
        
        const modal = document.createElement('div');
        modal.className = 'kms-modal-overlay';
        modal.innerHTML = `
            <div class="kms-modal-content kms-storage-manager">
                <div class="kms-modal-header">
                    <h3>${this.posterMaker.currentLanguage === 'en' ? 'Storage Manager' : '儲存管理器'}</h3>
                    <button class="kms-modal-close">&times;</button>
                </div>
                <div class="kms-modal-body">
                    <div class="kms-storage-stats">
                        <h4>${this.posterMaker.currentLanguage === 'en' ? 'Storage Statistics' : '儲存統計'}</h4>
                        <div class="kms-stats-grid">
                            <div class="kms-stat-item">
                                <span class="kms-stat-label">${this.posterMaker.currentLanguage === 'en' ? 'Total Size' : '總大小'}</span>
                                <span class="kms-stat-value">${storageUsage.mb}</span>
                            </div>
                            <div class="kms-stat-item">
                                <span class="kms-stat-label">${this.posterMaker.currentLanguage === 'en' ? 'Posters' : '海報數量'}</span>
                                <span class="kms-stat-value">${storageUsage.posterCount}</span>
                            </div>
                            <div class="kms-stat-item">
                                <span class="kms-stat-label">${this.posterMaker.currentLanguage === 'en' ? 'Images' : '圖片數量'}</span>
                                <span class="kms-stat-value">${storageUsage.imageCount}</span>
                            </div>
                        </div>
                    </div>
                    
                    <div class="kms-poster-list">
                        <h4>${this.posterMaker.currentLanguage === 'en' ? 'Saved Posters' : '已儲存的海報'}</h4>
                        <div class="kms-poster-grid">
                            ${savedPosters.posters.map(poster => `
                                <div class="kms-poster-item" data-poster-id="${poster.id}">
                                    <div class="kms-poster-info">
                                        <h5>${poster.name}</h5>
                                        <p>${poster.file_size_formatted} • ${poster.updated_at_formatted}</p>
                                    </div>
                                    <div class="kms-poster-actions">
                                        <button class="kms-btn kms-btn-primary kms-load-poster" data-poster-id="${poster.id}">
                                            ${this.posterMaker.currentLanguage === 'en' ? 'Load' : '載入'}
                                        </button>
                                        <button class="kms-btn kms-btn-danger kms-delete-poster" data-poster-id="${poster.id}">
                                            ${this.posterMaker.currentLanguage === 'en' ? 'Delete' : '刪除'}
                                        </button>
                                    </div>
                                </div>
                            `).join('')}
                        </div>
                    </div>
                </div>
            </div>
        `;
        
        document.body.appendChild(modal);
        
        // 綁定事件
        this.bindStorageManagerEvents(modal);
    }
    
    /**
     * 綁定儲存管理器事件
     */
    bindStorageManagerEvents(modal) {
        // 關閉按鈕
        modal.querySelector('.kms-modal-close').addEventListener('click', () => {
            modal.remove();
        });
        
        // 點擊背景關閉
        modal.addEventListener('click', (e) => {
            if (e.target === modal) {
                modal.remove();
            }
        });
        
        // 載入海報
        modal.querySelectorAll('.kms-load-poster').forEach(btn => {
            btn.addEventListener('click', async (e) => {
                const posterId = e.target.dataset.posterId;
                await this.loadPosterData(posterId);
                modal.remove();
            });
        });
        
        // 刪除海報
        modal.querySelectorAll('.kms-delete-poster').forEach(btn => {
            btn.addEventListener('click', async (e) => {
                const posterId = e.target.dataset.posterId;
                const posterName = e.target.closest('.kms-poster-item').querySelector('h5').textContent;
                
                if (confirm(this.posterMaker.currentLanguage === 'en' 
                    ? `Are you sure you want to delete "${posterName}"?`
                    : `確定要刪除「${posterName}」嗎？`)) {
                    
                    const success = await this.deleteSavedPoster(posterId);
                    if (success) {
                        e.target.closest('.kms-poster-item').remove();
                    }
                }
            });
        });
    }
    
    /**
     * 重新建立元素
     */
    async recreateElement(elementData) {
        let element;
        
        if (elementData.type === 'text') {
            element = this.posterMaker.textHandler.createTextElement(elementData.content);
        } else if (elementData.type === 'image') {
            element = await this.recreateImageElement(elementData);
        } else if (elementData.type === 'qr') {
            element = this.posterMaker.qrGenerator.createQRElement(elementData.content, elementData.qrSettings);
        }
        
        if (element) {
            // 設定位置和大小
            element.style.left = elementData.position.x + 'px';
            element.style.top = elementData.position.y + 'px';
            element.style.width = elementData.size.width + 'px';
            element.style.height = elementData.size.height + 'px';
            element.style.zIndex = elementData.zIndex || 1;
            
            // 應用樣式
            Object.keys(elementData.styles).forEach(key => {
                if (elementData.styles[key]) {
                    element.style[key] = elementData.styles[key];
                }
            });
            
            // 設定元素ID
            element.dataset.elementId = elementData.id;
            
            // 添加到畫布
            const canvas = document.getElementById('posterCanvas');
            canvas.appendChild(element);
            this.posterMaker.elements.push(element);
        }
    }
    
    /**
     * 重新建立圖片元素
     */
    async recreateImageElement(elementData) {
        const element = document.createElement('div');
        element.className = 'kms-image-element kms-draggable';
        
        const img = document.createElement('img');
        img.src = elementData.content;
        img.alt = elementData.alt || '';
        img.style.width = '100%';
        img.style.height = '100%';
        img.style.objectFit = 'contain';
        
        // 設定伺服器圖片資訊
        if (elementData.isServerImage) {
            element.dataset.serverFileName = elementData.serverFileName || '';
            element.dataset.serverPath = elementData.serverPath || '';
            element.dataset.isServerImage = 'true';
        }
        
        element.appendChild(img);
        
        // 綁定拖拽事件
        this.posterMaker.dragDrop.makeDraggable(element);
        
        return element;
    }
    
    // 輔助方法
    generateElementId() {
        return 'element_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
    }
    
    showSaveSuccessMessage(posterName, fileSize = '') {
        this.showToast(
            this.posterMaker.currentLanguage === 'en' 
                ? `Poster "${posterName}" saved successfully! ${fileSize ? `Size: ${fileSize}` : ''}`
                : `海報「${posterName}」儲存成功！${fileSize ? `大小：${fileSize}` : ''}`,
            'success'
        );
    }
    
    showLoadSuccessMessage(posterName) {
        this.showToast(
            this.posterMaker.currentLanguage === 'en' 
                ? `Poster "${posterName}" loaded successfully!`
                : `海報「${posterName}」載入成功！`,
            'success'
        );
    }
    
    showToast(message, type = 'info', duration = 3000) {
        const toast = document.createElement('div');
        toast.className = `kms-toast kms-toast-${type}`;
        toast.innerHTML = `
            <div class="kms-toast-content">
                <span class="kms-toast-message">${message}</span>
                <button class="kms-toast-close">&times;</button>
            </div>
        `;
        
        document.body.appendChild(toast);
        
        // 自動移除
        setTimeout(() => {
            if (toast.parentNode) {
                toast.remove();
            }
        }, duration);
        
        // 手動關閉
        toast.querySelector('.kms-toast-close').addEventListener('click', () => {
            toast.remove();
        });
        
        // 動畫效果
        setTimeout(() => {
            toast.classList.add('kms-toast-show');
        }, 10);
    }
    
    // 匯出和匯入功能（保持相容性）
    exportPosterAsJSON() {
        const posterData = this.savePosterData();
        const dataStr = JSON.stringify(posterData, null, 2);
        const dataBlob = new Blob([dataStr], { type: 'application/json' });
        
        const link = document.createElement('a');
        link.href = URL.createObjectURL(dataBlob);
        link.download = `${posterData.name || 'poster'}.json`;
        link.click();
    }
    
    importPosterFromJSON(file) {
        const reader = new FileReader();
        reader.onload = (e) => {
            try {
                const posterData = JSON.parse(e.target.result);
                this.loadPosterData(posterData);
            } catch (error) {
                alert(this.posterMaker.currentLanguage === 'en' 
                    ? 'Invalid JSON file format.'
                    : '無效的 JSON 檔案格式。');
            }
        };
        reader.readAsText(file);
    }
}
