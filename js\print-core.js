/**
 * <PERSON><PERSON> Poster Maker - Print Core Handler
 * 核心打印處理模塊
 */

class KMSPrintCore {
    constructor(posterMaker) {
        this.posterMaker = posterMaker;
        this.printPreview = new KMSPrintPreview(posterMaker);
        this.posterData = new KMSPosterData(posterMaker);
        this.init();
    }
    
    init() {
        this.setupPrintControls();
        this.createPrintStyles();
    }
    
    setupPrintControls() {
        const printBtn = document.getElementById('printBtn');
        if (printBtn) {
            printBtn.addEventListener('click', () => {
                this.printPoster();
            });
        }
        
        // Add keyboard shortcut Ctrl+P
        document.addEventListener('keydown', (e) => {
            if (e.ctrlKey && e.key === 'p') {
                e.preventDefault();
                this.printPoster();
            }
        });
    }
    
    createPrintStyles() {
        const printStyles = document.createElement('style');
        printStyles.id = 'kms-print-styles';
        printStyles.textContent = this.getPrintStylesCSS();
        document.head.appendChild(printStyles);
    }
    
    getPrintStylesCSS() {
        return `
            @media print {
                @page {
                    margin: 0 !important;
                    padding: 0 !important;
                    size: auto !important;
                    -webkit-print-color-adjust: exact !important;
                    color-adjust: exact !important;
                    print-color-adjust: exact !important;
                }
                
                @page :first {
                    margin: 0 !important;
                    padding: 0 !important;
                }
                
                @page :left {
                    margin: 0 !important;
                    padding: 0 !important;
                }
                
                @page :right {
                    margin: 0 !important;
                    padding: 0 !important;
                }
                
                * {
                    -webkit-print-color-adjust: exact !important;
                    color-adjust: exact !important;
                    print-color-adjust: exact !important;
                }
                
                html {
                    margin: 0 !important;
                    padding: 0 !important;
                    background: transparent !important;
                }
                
                body {
                    margin: 0 !important;
                    padding: 0 !important;
                    background: transparent !important;
                    -webkit-print-color-adjust: exact !important;
                    color-adjust: exact !important;
                    print-color-adjust: exact !important;
                }
                
                body * {
                    visibility: hidden;
                }
                
                .kms-poster-canvas,
                .kms-poster-canvas * {
                    visibility: visible !important;
                    -webkit-print-color-adjust: exact !important;
                    color-adjust: exact !important;
                    print-color-adjust: exact !important;
                }
                
                .kms-poster-canvas {
                    position: absolute !important;
                    left: 0 !important;
                    top: 0 !important;
                    width: 100% !important;
                    height: 100% !important;
                    max-width: none !important;
                    max-height: none !important;
                    transform: none !important;
                    box-shadow: none !important;
                    border-radius: 0 !important;
                }
                
                .kms-canvas-element.selected::after,
                .kms-resize-handle {
                    display: none !important;
                }
                
                .kms-canvas-grid {
                    display: none !important;
                }
                
                .kms-text-element {
                    word-wrap: break-word;
                    white-space: pre-wrap;
                    box-sizing: border-box !important;
                    transform: none !important;
                    zoom: 1 !important;
                    scale: 1 !important;
                }
                
                /* Letter size specific */
                .kms-canvas-letter {
                    width: 816px !important;
                    height: 1056px !important;
                }
                
                /* 4x6 size specific */
                .kms-canvas-4x6 {
                    width: 576px !important;
                    height: 384px !important;
                }
                
                /* Ensure all elements are properly sized for print */
                .kms-canvas-element {
                    position: absolute !important;
                    transform: none !important;
                }
                
                .kms-text-element {
                    word-wrap: break-word;
                    white-space: pre-wrap;
                }
                
                .kms-image-element {
                    overflow: hidden;
                }
                
                .kms-qr-element {
                    overflow: hidden;
                }
            }
        `;
    }
    
    printPoster() {
        // Show borderless print instructions first
        this.showBorderlessPrintInstructions();
    }
    
    showBorderlessPrintInstructions() {
        const overlay = document.createElement('div');
        overlay.className = 'kms-print-overlay';
        overlay.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.8);
            z-index: 10000;
            display: flex;
            justify-content: center;
            align-items: center;
        `;
        
        const dialog = document.createElement('div');
        dialog.className = 'kms-borderless-dialog';
        dialog.style.cssText = `
            background: white;
            padding: 2rem;
            border-radius: 8px;
            max-width: 500px;
            width: 90%;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
        `;
        
        const title = document.createElement('h2');
        title.textContent = this.posterMaker.currentLanguage === 'en' 
            ? 'Print Settings' 
            : '打印設置';
        title.style.cssText = `
            margin: 0 0 1rem 0;
            color: #333;
            font-size: 1.5rem;
        `;
        
        const instructions = document.createElement('div');
        instructions.style.cssText = `
            margin-bottom: 2rem;
            line-height: 1.6;
            color: #666;
        `;
        
        const instructionText = this.posterMaker.currentLanguage === 'en' ? 
            `<p><strong>For best results, please set your printer to:</strong></p>
            <ul>
                <li>Margins: None (or Minimum)</li>
                <li>Scale: 100% (Actual Size)</li>
                <li>Background graphics: On</li>
                <li>Paper size: ${this.posterMaker.currentPaperSize === 'letter' ? 'Letter (8.5" × 11")' : '4" × 6"'}</li>
            </ul>
            <p><em>Note: Different browsers may have slightly different options. Look for "Margins: None" or similar settings.</em></p>` :
            `<p><strong>為獲得最佳效果，請將打印機設置為：</strong></p>
            <ul>
                <li>邊距：無（或最小）</li>
                <li>縮放：100%（實際大小）</li>
                <li>背景圖形：開啟</li>
                <li>紙張大小：${this.posterMaker.currentPaperSize === 'letter' ? 'Letter (8.5" × 11")' : '4" × 6"'}</li>
            </ul>
            <p><em>注意：不同瀏覽器的選項可能略有不同。請尋找「邊距：無」或類似設置。</em></p>`;
        
        instructions.innerHTML = instructionText;
        
        const buttonsContainer = document.createElement('div');
        buttonsContainer.style.cssText = `
            display: flex;
            gap: 1rem;
            justify-content: flex-end;
        `;
        
        const continueBtn = document.createElement('button');
        continueBtn.textContent = this.posterMaker.currentLanguage === 'en' ? 'Continue to Print' : '繼續打印';
        continueBtn.className = 'kms-btn kms-btn-success';
        continueBtn.style.cssText = `
            background: #28a745;
            color: white;
            border: none;
            padding: 0.75rem 1.5rem;
            border-radius: 4px;
            cursor: pointer;
            font-size: 1rem;
        `;
        
        const cancelBtn = document.createElement('button');
        cancelBtn.textContent = this.posterMaker.currentLanguage === 'en' ? 'Cancel' : '取消';
        cancelBtn.className = 'kms-btn kms-btn-secondary';
        cancelBtn.style.cssText = `
            background: #6c757d;
            color: white;
            border: none;
            padding: 0.75rem 1.5rem;
            border-radius: 4px;
            cursor: pointer;
            font-size: 1rem;
        `;
        
        continueBtn.addEventListener('click', () => {
            document.body.removeChild(overlay);
            this.showPrintDialog();
        });
        
        cancelBtn.addEventListener('click', () => {
            document.body.removeChild(overlay);
        });
        
        buttonsContainer.appendChild(continueBtn);
        buttonsContainer.appendChild(cancelBtn);
        
        dialog.appendChild(title);
        dialog.appendChild(instructions);
        dialog.appendChild(buttonsContainer);
        overlay.appendChild(dialog);
        
        document.body.appendChild(overlay);
    }
    
    showPrintDialog() {
        // Create print dialog overlay
        const overlay = document.createElement('div');
        overlay.className = 'kms-print-overlay';
        overlay.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.8);
            z-index: 10000;
            display: flex;
            justify-content: center;
            align-items: center;
        `;
        
        const dialog = document.createElement('div');
        dialog.className = 'kms-print-dialog';
        dialog.style.cssText = `
            background: white;
            padding: 2rem;
            border-radius: 8px;
            max-width: 400px;
            width: 90%;
        `;
        
        const title = document.createElement('h2');
        title.textContent = this.posterMaker.currentLanguage === 'en' ? 'Print Options' : '打印選項';
        title.style.cssText = `
            margin: 0 0 1.5rem 0;
            color: #333;
        `;
        
        // Buttons
        const buttonsContainer = document.createElement('div');
        buttonsContainer.style.cssText = `
            display: flex;
            gap: 1rem;
            justify-content: flex-end;
        `;
        
        const cancelBtn = document.createElement('button');
        cancelBtn.textContent = this.posterMaker.currentLanguage === 'en' ? 'Cancel' : '取消';
        cancelBtn.className = 'kms-btn kms-btn-secondary';
        cancelBtn.addEventListener('click', () => {
            document.body.removeChild(overlay);
        });
        
        const previewBtn = document.createElement('button');
        previewBtn.textContent = this.posterMaker.currentLanguage === 'en' ? 'Preview' : '預覽';
        previewBtn.className = 'kms-btn';
        previewBtn.addEventListener('click', () => {
            document.body.removeChild(overlay);
            this.printPreview.showPrintPreview();
        });
        
        const printBtn = document.createElement('button');
        printBtn.textContent = this.posterMaker.currentLanguage === 'en' ? 'Print' : '打印';
        printBtn.className = 'kms-btn kms-btn-success';
        printBtn.addEventListener('click', () => {
            document.body.removeChild(overlay);
            this.executePrint();
        });
        
        buttonsContainer.appendChild(cancelBtn);
        buttonsContainer.appendChild(previewBtn);
        buttonsContainer.appendChild(printBtn);
        
        dialog.appendChild(title);
        dialog.appendChild(buttonsContainer);
        overlay.appendChild(dialog);
        document.body.appendChild(overlay);
        
        // Close on overlay click
        overlay.addEventListener('click', (e) => {
            if (e.target === overlay) {
                document.body.removeChild(overlay);
            }
        });
    }
    
    injectPrintStyles() {
        // 移除舊的打印樣式
        const existingStyle = document.getElementById('dynamic-print-styles');
        if (existingStyle) {
            existingStyle.remove();
        }
        
        // 創建新的打印樣式
        const style = document.createElement('style');
        style.id = 'dynamic-print-styles';
        style.textContent = `
            @media print {
                @page {
                    margin: 0 !important;
                    padding: 0 !important;
                    size: letter !important;
                    -webkit-print-color-adjust: exact !important;
                    color-adjust: exact !important;
                    print-color-adjust: exact !important;
                }
                
                * {
                    -webkit-print-color-adjust: exact !important;
                    color-adjust: exact !important;
                    print-color-adjust: exact !important;
                }
                
                html, body {
                    margin: 0 !important;
                    padding: 0 !important;
                    width: 100% !important;
                    height: 100% !important;
                    transform: none !important;
                    zoom: 1 !important;
                    scale: 1 !important;
                    overflow: visible !important;
                }
                
                #posterCanvas {
                    position: absolute !important;
                    left: 0 !important;
                    top: 0 !important;
                    margin: 0 !important;
                    padding: 0 !important;
                    border: none !important;
                    box-shadow: none !important;
                    transform: none !important;
                    transform-origin: top left !important;
                    zoom: 1 !important;
                    scale: 1 !important;
                    overflow: visible !important;
                    width: 8.5in !important;
                    height: 11in !important;
                    min-width: 8.5in !important;
                    min-height: 11in !important;
                    max-width: 8.5in !important;
                    max-height: 11in !important;
                }
                
                .kms-text-element {
                    font-family: inherit !important;
                    font-size: inherit !important;
                    color: inherit !important;
                    border-width: inherit !important;
                    border-style: inherit !important;
                    border-color: inherit !important;
                    background-color: inherit !important;
                    text-shadow: inherit !important;
                    text-align: inherit !important;
                    width: inherit !important;
                    height: inherit !important;
                    position: inherit !important;
                    left: inherit !important;
                    top: inherit !important;
                    transform: inherit !important;
                    -webkit-print-color-adjust: exact !important;
                    color-adjust: exact !important;
                    print-color-adjust: exact !important;
                }
                
                .kms-image-element {
                    -webkit-print-color-adjust: exact !important;
                    color-adjust: exact !important;
                    print-color-adjust: exact !important;
                }
            }
        `;
        
        document.head.appendChild(style);
        
        // 在打印完成後移除樣式
        const mediaQueryList = window.matchMedia('print');
        const handlePrintEnd = (mql) => {
            if (!mql.matches) {
                setTimeout(() => {
                    const styleToRemove = document.getElementById('dynamic-print-styles');
                    if (styleToRemove) {
                        styleToRemove.remove();
                    }
                }, 100);
            }
            mediaQueryList.removeListener(handlePrintEnd);
        };
        
        mediaQueryList.addListener(handlePrintEnd);
    }

    executePrint() {
        // 注入動態打印樣式
        this.injectPrintStyles();
        
        // 準備打印佈局
        this.preparePrintLayout();
        
        // 清除選擇狀態
        if (this.posterMaker.selectedElement) {
            this.posterMaker.selectedElement.classList.remove('selected');
            this.posterMaker.selectedElement = null;
        }
        
        // 執行打印（稍微延遲以確保樣式生效）
        setTimeout(() => {
            window.print();
        }, 100);
    }
    
    preparePrintLayout() {
        const canvas = document.getElementById('posterCanvas');
        if (!canvas) return;
        
        // Store original styles
        const originalStyles = {
            transform: canvas.style.transform,
            transformOrigin: canvas.style.transformOrigin,
            boxShadow: canvas.style.boxShadow,
            position: canvas.style.position,
            left: canvas.style.left,
            top: canvas.style.top,
            margin: canvas.style.margin,
            padding: canvas.style.padding,
            width: canvas.style.width,
            height: canvas.style.height,
            maxWidth: canvas.style.maxWidth,
            maxHeight: canvas.style.maxHeight,
            minWidth: canvas.style.minWidth,
            minHeight: canvas.style.minHeight,
            zoom: canvas.style.zoom,
            scale: canvas.style.scale
        };
        
        // 強制重置所有可能影響打印的樣式
        canvas.style.cssText = '';
        
        // Apply print-ready styles with highest priority
        canvas.style.setProperty('position', 'absolute', 'important');
        canvas.style.setProperty('left', '0', 'important');
        canvas.style.setProperty('top', '0', 'important');
        canvas.style.setProperty('margin', '0', 'important');
        canvas.style.setProperty('padding', '0', 'important');
        canvas.style.setProperty('transform', 'none', 'important');
        canvas.style.setProperty('transform-origin', 'top left', 'important');
        canvas.style.setProperty('box-shadow', 'none', 'important');
        canvas.style.setProperty('border-radius', '0', 'important');
        canvas.style.setProperty('border', 'none', 'important');
        canvas.style.setProperty('zoom', '1', 'important');
        canvas.style.setProperty('scale', '1', 'important');
        canvas.style.setProperty('overflow', 'visible', 'important');
        
        // 確保畫布填滿整個頁面
        if (canvas.classList.contains('kms-canvas-letter')) {
            canvas.style.setProperty('width', '8.5in', 'important');
            canvas.style.setProperty('height', '11in', 'important');
            canvas.style.setProperty('min-width', '8.5in', 'important');
            canvas.style.setProperty('min-height', '11in', 'important');
            canvas.style.setProperty('max-width', '8.5in', 'important');
            canvas.style.setProperty('max-height', '11in', 'important');
        }
        
        // 強制設定 body 和 html 樣式
        const body = document.body;
        const html = document.documentElement;
        
        const originalBodyStyles = {
            margin: body.style.margin,
            padding: body.style.padding,
            transform: body.style.transform,
            zoom: body.style.zoom
        };
        
        const originalHtmlStyles = {
            margin: html.style.margin,
            padding: html.style.padding,
            transform: html.style.transform,
            zoom: html.style.zoom
        };
        
        body.style.setProperty('margin', '0', 'important');
        body.style.setProperty('padding', '0', 'important');
        body.style.setProperty('transform', 'none', 'important');
        body.style.setProperty('zoom', '1', 'important');
        
        html.style.setProperty('margin', '0', 'important');
        html.style.setProperty('padding', '0', 'important');
        html.style.setProperty('transform', 'none', 'important');
        html.style.setProperty('zoom', '1', 'important');
        
        // Restore after print (when print dialog closes)
        const mediaQueryList = window.matchMedia('print');
        const handlePrintEnd = (mql) => {
            if (!mql.matches) {
                // 恢復畫布樣式
                Object.keys(originalStyles).forEach(key => {
                    if (originalStyles[key]) {
                        canvas.style[key] = originalStyles[key];
                    } else {
                        canvas.style.removeProperty(key);
                    }
                });
                
                // 恢復 body 和 html 樣式
                Object.keys(originalBodyStyles).forEach(key => {
                    if (originalBodyStyles[key]) {
                        body.style[key] = originalBodyStyles[key];
                    } else {
                        body.style.removeProperty(key);
                    }
                });
                
                Object.keys(originalHtmlStyles).forEach(key => {
                    if (originalHtmlStyles[key]) {
                        html.style[key] = originalHtmlStyles[key];
                    } else {
                        html.style.removeProperty(key);
                    }
                });
            }
            mediaQueryList.removeListener(handlePrintEnd);
        };
        
        mediaQueryList.addListener(handlePrintEnd);
    }
    
    // Delegate methods to appropriate modules
    savePosterData(posterName) {
        return this.posterData.savePosterData(posterName);
    }
    
    loadPosterData(posterData) {
        return this.posterData.loadPosterData(posterData);
    }
    
    exportAsImage(format, quality) {
        return this.printPreview.exportAsImage(format, quality);
    }
    
    showPrintPreview() {
        return this.printPreview.showPrintPreview();
    }
}
