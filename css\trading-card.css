/**
 * Trading Card CSS Styles
 * 交易卡片樣式
 */

/* 基礎樣式 */
:root {
  --trading-card-bg: var(--color-1);
  --trading-card-border: var(--border-color-1);
  --trading-card-text: var(--text-color-1);
  --trading-card-hover: var(--color-2);
  --trading-card-selected: var(--color-3);
  --trading-card-shadow: rgba(0, 0, 0, 0.1);
}

/* Trading Card Canvas */
.kms-canvas-trading-card {
  width: 240px !important;
  height: 336px !important;
  background: white;
  border: 2px solid var(--trading-card-border);
  border-radius: 8px;
  box-shadow: 0 4px 8px var(--trading-card-shadow);
}

/* Trading Card Layout Section */
#tradingCardLayoutSection {
  display: none;
  margin-top: 15px;
  padding: 15px;
  background: var(--trading-card-bg);
  border-radius: 8px;
  border: 1px solid var(--trading-card-border);
}

.kms-trading-card-title {
  font-size: 16px;
  font-weight: bold;
  color: var(--trading-card-text);
  margin-bottom: 10px;
  text-align: center;
}

/* Paper Type Selection */
.kms-trading-card-paper-options {
  display: flex;
  gap: 10px;
  margin-bottom: 15px;
}

/* 編輯模式選擇樣式 */
.kms-radio-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
  margin-top: 8px;
}

.kms-radio-option {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  border: 1px solid var(--border-color-1);
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s ease;
  background: var(--color-1);
}

.kms-radio-option:hover {
  background: var(--color-2);
  border-color: var(--color-3);
}

.kms-radio-option input[type="radio"] {
  margin: 0;
  cursor: pointer;
}

.kms-radio-option input[type="radio"]:checked + span {
  font-weight: 600;
  color: var(--color-3);
}

/* 同步模式信息樣式 */
.kms-sync-info {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 10px 12px;
  background: var(--color-2);
  border: 1px solid var(--color-3);
  border-radius: 6px;
  font-size: 13px;
  color: var(--text-color-1);
  margin-bottom: 10px;
}

.kms-sync-info i {
  color: var(--color-3);
  font-size: 14px;
}

/* 同步模式主卡片樣式 */
.kms-sync-master-card {
  position: relative;
}

.kms-sync-master-card::before {
  content: "主卡片";
  position: absolute;
  top: -8px;
  left: 8px;
  background: #007bff;
  color: white;
  padding: 2px 8px;
  border-radius: 4px;
  font-size: 11px;
  font-weight: 600;
  z-index: 10;
}

/* 編輯模式控制區域 */
#singleModeControls,
#syncModeControls {
  margin-top: 10px;
}

#syncModeControls {
  border-top: 1px solid var(--border-color-1);
  padding-top: 15px;
}

.kms-trading-card-paper-option {
  flex: 1;
  padding: 8px 12px;
  background: white;
  border: 2px solid var(--trading-card-border);
  border-radius: 6px;
  cursor: pointer;
  text-align: center;
  font-size: 12px;
  color: var(--trading-card-text);
  transition: all 0.3s ease;
}

.kms-trading-card-paper-option:hover {
  background: var(--trading-card-hover);
  transform: translateY(-2px);
}

.kms-trading-card-paper-option.active {
  background: var(--trading-card-selected);
  border-color: var(--border-color-3);
  color: white;
}

/* Action Buttons */
.kms-trading-card-actions {
  display: flex;
  gap: 10px;
}

.kms-trading-card-btn {
  flex: 1;
  padding: 10px 15px;
  background: var(--color-4);
  color: white;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 12px;
  font-weight: bold;
  transition: all 0.3s ease;
}

.kms-trading-card-btn:hover {
  background: var(--color-5);
  transform: translateY(-2px);
  box-shadow: 0 4px 8px var(--trading-card-shadow);
}

.kms-trading-card-btn:active {
  transform: translateY(0);
}

/* 布局樣式 */
/* Trading Card Grid Container */
.kms-trading-card-grid {
  display: grid;
  gap: 10px;
  padding: 20px;
  background: #f5f5f5;
  border-radius: 8px;
  margin: 20px 0;
}

/* Letter size: 3x3 grid */
.kms-trading-card-grid.letter-layout {
  grid-template-columns: repeat(3, 1fr);
  grid-template-rows: repeat(3, 1fr);
  width: 816px;
  height: 1056px;
}

/* 4x6 size: 1x2 grid */
.kms-trading-card-grid.photo-layout {
  grid-template-columns: repeat(1, 1fr);
  grid-template-rows: repeat(2, 1fr);
  width: 384px;
  height: 576px;
}

/* Individual Trading Card */
.kms-trading-card-item {
  background: white;
  border: 2px solid var(--trading-card-border);
  border-radius: 8px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  transition: all 0.3s ease;
  display: flex;
  flex-direction: column;
}

.kms-trading-card-item:hover {
  border-color: var(--border-color-2);
  transform: scale(1.02);
  box-shadow: 0 6px 12px var(--trading-card-shadow);
}

.kms-trading-card-item.selected {
  border-color: var(--border-color-3);
  background: var(--trading-card-selected);
  box-shadow: 0 0 0 3px rgba(47, 255, 92, 0.3);
}

/* Trading Card Content Areas */
.kms-trading-card-content {
  flex: 1;
  padding: 10px;
  position: relative;
  min-height: 100px;
  overflow: hidden;
  width: 100%;
  height: 100%;
}

/* 卡片元素基礎樣式 */
.kms-trading-card-element {
  margin: 2px 0;
  max-width: 100%;
  word-wrap: break-word;
  text-align: center;
  position: relative;
}

/* 文字元素 */
.kms-trading-card-text-element {
  position: absolute;
  cursor: move;
  -webkit-user-select: none;
  user-select: none;
  border: 1px dashed transparent;
  box-sizing: border-box;
  font-size: 10px;
  color: var(--text-color-1);
  text-align: center;
  word-wrap: break-word;
  line-height: 1.2;
  padding: 1px 2px;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 2px;
}

.kms-trading-card-text-element:hover {
  border-color: #007bff;
}

.kms-trading-card-text-element.selected {
  border-color: #007bff;
  box-shadow: 0 0 0 1px #007bff;
}

/* 圖片元素 */
.kms-trading-card-image-element {
  position: absolute;
  cursor: move;
  -webkit-user-select: none;
  user-select: none;
  border: 1px dashed transparent;
  box-sizing: border-box;
  display: flex;
  align-items: center;
  justify-content: center;
}

.kms-trading-card-image-element:hover {
  border-color: #007bff;
}

.kms-trading-card-image-element.selected {
  border-color: #007bff;
  box-shadow: 0 0 0 1px #007bff;
}

.kms-trading-card-image-element img {
  width: 100%;
  height: 100%;
  object-fit: contain;
  border-radius: 2px;
  pointer-events: none;
}

/* QR Code 元素 */
.kms-trading-card-qr-element {
  position: absolute;
  cursor: move;
  -webkit-user-select: none;
  user-select: none;
  border: 1px dashed transparent;
  box-sizing: border-box;
  display: flex;
  align-items: center;
  justify-content: center;
}

.kms-trading-card-qr-element:hover {
  border-color: #007bff;
}

.kms-trading-card-qr-element.selected {
  border-color: #007bff;
  box-shadow: 0 0 0 1px #007bff;
}

.kms-trading-card-qr-element img {
  width: 100%;
  height: 100%;
  border-radius: 2px;
  pointer-events: none;
}

/* 舊版相容性樣式 */
.kms-trading-card-text {
  font-size: 14px;
  color: var(--trading-card-text);
  text-align: center;
  word-wrap: break-word;
  margin-bottom: 5px;
}

.kms-trading-card-image {
  max-width: 100%;
  max-height: 80px;
  object-fit: contain;
  border-radius: 4px;
  margin-bottom: 5px;
}

.kms-trading-card-qr {
  width: 60px;
  height: 60px;
  margin: 5px auto;
}

/* Card Number */
.kms-trading-card-number {
  position: absolute;
  top: 5px;
  right: 5px;
  background: var(--color-4);
  color: white;
  padding: 2px 6px;
  border-radius: 10px;
  font-size: 10px;
  font-weight: bold;
}

/* 交互樣式 */
/* Message Display */
.kms-trading-card-message {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: var(--color-1);
  color: white;
  padding: 15px 25px;
  border-radius: 8px;
  font-size: 14px;
  font-weight: bold;
  z-index: 10000;
  box-shadow: 0 4px 12px var(--trading-card-shadow);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.kms-trading-card-message.show {
  opacity: 1;
}

/* Loading State */
.kms-trading-card-loading {
  position: relative;
}

.kms-trading-card-loading::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 20px;
  height: 20px;
  margin: -10px 0 0 -10px;
  border: 2px solid var(--trading-card-border);
  border-top: 2px solid var(--color-1);
  border-radius: 50%;
  animation: kms-trading-card-spin 1s linear infinite;
}

@keyframes kms-trading-card-spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 響應式樣式 */
@media (max-width: 768px) {
  .kms-trading-card-grid.letter-layout {
    grid-template-columns: repeat(2, 1fr);
    grid-template-rows: repeat(5, 1fr);
    width: 100%;
    max-width: 500px;
  }
  
  .kms-trading-card-grid.photo-layout {
    grid-template-columns: repeat(1, 1fr);
    grid-template-rows: repeat(2, 1fr);
    width: 100%;
    max-width: 300px;
  }
  
  .kms-trading-card-paper-options {
    flex-direction: column;
  }
  
  .kms-trading-card-actions {
    flex-direction: column;
  }
}

@media (max-width: 480px) {
  .kms-trading-card-grid.letter-layout {
    grid-template-columns: repeat(1, 1fr);
    grid-template-rows: repeat(9, 1fr);
  }
  
  #tradingCardLayoutSection {
    padding: 10px;
  }
  
  .kms-trading-card-btn {
    padding: 12px 15px;
    font-size: 14px;
  }
}

/* Print Styles */
@media print {
  .kms-trading-card-grid {
    background: white;
    margin: 0;
    padding: 0;
  }
  
  .kms-trading-card-item {
    break-inside: avoid;
    border: 1px solid #000;
  }
  
  .kms-trading-card-number {
    display: none;
  }
}