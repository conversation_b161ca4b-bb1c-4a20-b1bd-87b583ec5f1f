<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, DELETE');
header('Access-Control-Allow-Headers: Content-Type');

$uploadDir = '../uploads/images/';
$response = array();

// 確保上傳目錄存在
if (!file_exists($uploadDir)) {
    mkdir($uploadDir, 0755, true);
}

$method = $_SERVER['REQUEST_METHOD'];

switch ($method) {
    case 'GET':
        // 列出所有圖片
        try {
            $images = array();
            $files = scandir($uploadDir);
            
            foreach ($files as $file) {
                if ($file !== '.' && $file !== '..' && is_file($uploadDir . $file)) {
                    $filePath = $uploadDir . $file;
                    $fileInfo = array(
                        'filename' => $file,
                        'path' => 'uploads/images/' . $file,
                        'url' => 'uploads/images/' . $file,
                        'size' => filesize($filePath),
                        'modified' => filemtime($filePath),
                        'type' => mime_content_type($filePath)
                    );
                    $images[] = $fileInfo;
                }
            }
            
            // 按修改時間排序（最新的在前）
            usort($images, function($a, $b) {
                return $b['modified'] - $a['modified'];
            });
            
            $response['success'] = true;
            $response['images'] = $images;
            $response['count'] = count($images);
            
        } catch (Exception $e) {
            $response['success'] = false;
            $response['message'] = $e->getMessage();
        }
        break;
        
    case 'DELETE':
        // 刪除指定圖片
        try {
            $input = json_decode(file_get_contents('php://input'), true);
            
            if (!isset($input['filename'])) {
                throw new Exception('Filename not provided');
            }
            
            $filename = basename($input['filename']); // 安全處理文件名
            $filePath = $uploadDir . $filename;
            
            if (!file_exists($filePath)) {
                throw new Exception('File not found');
            }
            
            if (unlink($filePath)) {
                $response['success'] = true;
                $response['message'] = 'Image deleted successfully';
            } else {
                throw new Exception('Failed to delete file');
            }
            
        } catch (Exception $e) {
            $response['success'] = false;
            $response['message'] = $e->getMessage();
        }
        break;
        
    case 'POST':
        // 清理舊圖片（刪除超過指定天數的圖片）
        try {
            $input = json_decode(file_get_contents('php://input'), true);
            $daysOld = isset($input['days']) ? intval($input['days']) : 30; // 默認30天
            
            $deletedCount = 0;
            $files = scandir($uploadDir);
            $cutoffTime = time() - ($daysOld * 24 * 60 * 60);
            
            foreach ($files as $file) {
                if ($file !== '.' && $file !== '..' && is_file($uploadDir . $file)) {
                    $filePath = $uploadDir . $file;
                    if (filemtime($filePath) < $cutoffTime) {
                        if (unlink($filePath)) {
                            $deletedCount++;
                        }
                    }
                }
            }
            
            $response['success'] = true;
            $response['message'] = "Cleaned up $deletedCount old images";
            $response['deleted_count'] = $deletedCount;
            
        } catch (Exception $e) {
            $response['success'] = false;
            $response['message'] = $e->getMessage();
        }
        break;
        
    default:
        $response['success'] = false;
        $response['message'] = 'Invalid request method';
        break;
}

echo json_encode($response);
?>