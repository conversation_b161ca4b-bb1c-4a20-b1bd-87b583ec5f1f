/**
 * <PERSON><PERSON> Poster Maker - File Manager Styles
 * Styles for file management modal dialogs and poster lists
 */

/* Modal Overlay */
.kms-modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.7);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 10000;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

.kms-modal-overlay.kms-modal-show {
    opacity: 1;
    visibility: visible;
}

/* Modal Container */
.kms-modal {
    background: var(--text-color-2);
    border-radius: 12px;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
    max-height: 90vh;
    overflow: hidden;
    transform: scale(0.9) translateY(-20px);
    transition: all 0.3s ease;
    border: 2px solid var(--border-color-1);
}

.kms-modal-show .kms-modal {
    transform: scale(1) translateY(0);
}

.kms-modal-medium {
    width: 90%;
    max-width: 500px;
}

.kms-modal-large {
    width: 90%;
    max-width: 800px;
}

/* Modal Header */
.kms-modal-header {
    background: linear-gradient(135deg, var(--color-1), var(--color-2));
    color: var(--text-color-2);
    padding: 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 2px solid var(--border-color-1);
}

.kms-modal-title {
    margin: 0;
    font-size: 1.4em;
    font-weight: 600;
}

.kms-modal-close {
    background: none;
    border: none;
    color: var(--text-color-2);
    font-size: 1.5em;
    cursor: pointer;
    padding: 5px;
    border-radius: 50%;
    transition: all 0.3s ease;
    width: 35px;
    height: 35px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.kms-modal-close:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: rotate(90deg);
}

/* Modal Content */
.kms-modal-content {
    padding: 25px;
    max-height: calc(90vh - 100px);
    overflow-y: auto;
}

/* Form Styles */
.kms-form-group {
    margin-bottom: 20px;
}

.kms-form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 600;
    color: var(--text-color-1);
    font-size: 0.95em;
}

.kms-input {
    width: 100%;
    padding: 12px 15px;
    border: 2px solid var(--border-color-1);
    border-radius: 8px;
    font-size: 1em;
    background: var(--text-color-2);
    color: var(--text-color-1);
    transition: all 0.3s ease;
    box-sizing: border-box;
}

.kms-input:focus {
    outline: none;
    border-color: var(--color-1);
    box-shadow: 0 0 0 3px rgba(0, 200, 255, 0.1);
}

.kms-file-input {
    width: 100%;
    padding: 10px;
    border: 2px dashed var(--border-color-1);
    border-radius: 8px;
    background: rgba(0, 200, 255, 0.05);
    cursor: pointer;
    transition: all 0.3s ease;
    margin-bottom: 10px;
}

.kms-file-input:hover {
    border-color: var(--color-1);
    background: rgba(0, 200, 255, 0.1);
}

/* Button Styles */
.kms-btn {
    padding: 10px 20px;
    border: none;
    border-radius: 6px;
    font-size: 0.9em;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-block;
    text-align: center;
    margin: 0 5px;
}

.kms-btn-primary {
    background: var(--color-1);
    color: var(--text-color-2);
}

.kms-btn-primary:hover {
    background: var(--border-color-1);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 200, 255, 0.3);
}

.kms-btn-secondary {
    background: var(--color-2);
    color: var(--text-color-1);
}

.kms-btn-secondary:hover {
    background: var(--border-color-2);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(255, 200, 0, 0.3);
}

.kms-btn-info {
    background: var(--color-5);
    color: var(--text-color-1);
}

.kms-btn-info:hover {
    background: var(--border-color-5);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 255, 234, 0.3);
}

.kms-btn-danger {
    background: var(--color-4);
    color: var(--text-color-2);
}

.kms-btn-danger:hover {
    background: var(--border-color-4);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(255, 49, 49, 0.3);
}

.kms-btn-warning {
    background: var(--color-2);
    color: var(--text-color-1);
}

.kms-btn-warning:hover {
    background: var(--border-color-2);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(255, 200, 0, 0.3);
}

.kms-btn-small {
    padding: 6px 12px;
    font-size: 0.8em;
    margin: 0 2px;
}

/* Form Actions */
.kms-form-actions {
    display: flex;
    justify-content: flex-end;
    gap: 10px;
    margin-top: 25px;
    padding-top: 20px;
    border-top: 1px solid var(--border-color-1);
}

/* Import Section */
.kms-import-section {
    background: rgba(0, 255, 234, 0.05);
    padding: 20px;
    border-radius: 8px;
    border: 1px solid var(--border-color-5);
    margin-bottom: 25px;
}

.kms-import-label {
    display: block;
    margin-bottom: 10px;
    font-weight: 600;
    color: var(--text-color-1);
}

/* Posters List */
.kms-posters-list {
    max-height: 400px;
    overflow-y: auto;
    border: 1px solid var(--border-color-1);
    border-radius: 8px;
    background: rgba(0, 200, 255, 0.02);
}

.kms-poster-item {
    display: flex;
    align-items: center;
    padding: 15px 20px;
    border-bottom: 1px solid var(--border-color-1);
    transition: all 0.3s ease;
    gap: 15px;
}

.kms-poster-item:last-child {
    border-bottom: none;
}

.kms-poster-item:hover {
    background: rgba(0, 200, 255, 0.08);
    transform: translateX(5px);
}

/* 縮圖樣式 */
.kms-poster-thumbnail {
    flex-shrink: 0;
    width: 80px;
    height: 60px;
    border: 1px solid var(--border-color-1);
    border-radius: 4px;
    overflow: hidden;
    background: var(--text-color-2);
    display: flex;
    align-items: center;
    justify-content: center;
}

.kms-thumbnail-img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    display: block;
}

.kms-thumbnail-placeholder {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    color: var(--text-color-1);
    font-size: 10px;
    text-align: center;
    padding: 5px;
    opacity: 0.5;
}

.kms-thumbnail-placeholder i {
    font-size: 16px;
    margin-bottom: 2px;
    color: var(--text-color-1);
}

.kms-poster-info {
    flex: 1;
    min-width: 0;
}

.kms-poster-name {
    margin: 0 0 5px 0;
    font-size: 1.1em;
    font-weight: 600;
    color: var(--text-color-1);
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.kms-poster-details {
    margin: 0;
    font-size: 0.85em;
    color: var(--text-color-1);
    opacity: 0.7;
    line-height: 1.4;
}

.kms-poster-actions {
    display: flex;
    gap: 5px;
    flex-shrink: 0;
}

/* Manage Dialog */
.kms-manage-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding: 15px 20px;
    background: rgba(47, 255, 92, 0.1);
    border-radius: 8px;
    border: 1px solid var(--border-color-3);
}

.kms-manage-info {
    margin: 0;
    font-weight: 600;
    color: var(--text-color-1);
}

/* No Posters Message */
.kms-no-posters {
    text-align: center;
    padding: 40px 20px;
    color: var(--text-color-1);
}

.kms-no-posters p {
    margin: 0 0 15px 0;
    font-size: 1.1em;
    line-height: 1.6;
}

.kms-no-posters p:first-child {
    font-weight: 600;
    color: var(--color-1);
}

/* Scrollbar Styles */
.kms-modal-content::-webkit-scrollbar,
.kms-posters-list::-webkit-scrollbar {
    width: 8px;
}

.kms-modal-content::-webkit-scrollbar-track,
.kms-posters-list::-webkit-scrollbar-track {
    background: rgba(0, 0, 0, 0.1);
    border-radius: 4px;
}

.kms-modal-content::-webkit-scrollbar-thumb,
.kms-posters-list::-webkit-scrollbar-thumb {
    background: var(--color-1);
    border-radius: 4px;
}

.kms-modal-content::-webkit-scrollbar-thumb:hover,
.kms-posters-list::-webkit-scrollbar-thumb:hover {
    background: var(--border-color-1);
}

/* Animation Keyframes */
@keyframes slideInRight {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

@keyframes slideOutRight {
    from {
        transform: translateX(0);
        opacity: 1;
    }
    to {
        transform: translateX(100%);
        opacity: 0;
    }
}

/* Responsive Design */
@media (max-width: 768px) {
    .kms-modal-medium,
    .kms-modal-large {
        width: 95%;
        margin: 10px;
    }
    
    .kms-modal-content {
        padding: 20px;
    }
    
    .kms-poster-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
    }
    
    .kms-poster-actions {
        width: 100%;
        justify-content: flex-end;
    }
    
    .kms-form-actions {
        flex-direction: column;
        gap: 10px;
    }
    
    .kms-btn {
        width: 100%;
        margin: 0;
    }
    
    .kms-manage-header {
        flex-direction: column;
        gap: 10px;
        text-align: center;
    }
    
    .kms-search-container {
        margin-bottom: 15px;
    }
    
    .kms-pagination-controls {
        flex-wrap: wrap;
        gap: 5px;
    }
    
    .kms-pagination-info {
        text-align: center;
        margin-top: 10px;
        font-size: 0.9em;
    }
}

/* Search Container Styles */
.kms-search-container {
    margin-bottom: 20px;
    padding: 15px;
    background: linear-gradient(135deg, var(--color-5), var(--color-3));
    border-radius: 8px;
    border: 2px solid var(--border-color-5);
}

.kms-search-input {
    width: 100%;
    padding: 12px 15px;
    border: 2px solid var(--border-color-1);
    border-radius: 6px;
    font-size: 1em;
    background: var(--text-color-2);
    color: var(--text-color-1);
    transition: all 0.3s ease;
}

.kms-search-input:focus {
    outline: none;
    border-color: var(--border-color-3);
    box-shadow: 0 0 10px rgba(30, 255, 0, 0.3);
}

.kms-search-input::placeholder {
    color: #999;
    font-style: italic;
}

/* Pagination Styles */
.kms-pagination-container {
    margin-top: 20px;
    padding: 15px;
    background: linear-gradient(135deg, var(--color-1), var(--color-2));
    border-radius: 8px;
    border: 2px solid var(--border-color-1);
}

.kms-pagination-controls {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 8px;
    flex-wrap: wrap;
    margin-bottom: 10px;
}

.kms-pagination-dots {
    color: var(--text-color-2);
    font-weight: bold;
    padding: 0 5px;
}

.kms-pagination-info {
    text-align: center;
    color: var(--text-color-2);
    font-size: 0.95em;
    font-weight: 500;
    margin-top: 10px;
}

/* Button Variations for Pagination */
.kms-btn-small {
    padding: 8px 12px;
    font-size: 0.9em;
    min-width: 40px;
}

.kms-btn-secondary {
    background: linear-gradient(135deg, #6c757d, #5a6268);
    border-color: #6c757d;
}

.kms-btn-secondary:hover {
    background: linear-gradient(135deg, #5a6268, #495057);
    border-color: #5a6268;
    transform: translateY(-2px);
}

/* Enhanced Poster List Container */
.kms-posters-list-container {
    min-height: 200px;
    max-height: 400px;
    overflow-y: auto;
    border: 2px solid var(--border-color-2);
    border-radius: 8px;
    background: rgba(255, 255, 255, 0.05);
    padding: 10px;
}

.kms-posters-list-container:empty::before {
    content: "沒有找到海報 / No posters found";
    display: block;
    text-align: center;
    color: #999;
    font-style: italic;
    padding: 50px 20px;
}

/* Loading State */
.kms-loading {
    text-align: center;
    padding: 40px 20px;
    color: var(--text-color-1);
}

.kms-loading::before {
    content: "載入中... / Loading...";
    display: block;
    font-style: italic;
}

/* Enhanced Poster Item Hover Effects */
.kms-poster-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
    border-color: var(--border-color-3);
}

.kms-poster-item:hover .kms-poster-thumbnail img {
    transform: scale(1.05);
}

/* Smooth Transitions */
.kms-posters-list-container,
.kms-pagination-container,
.kms-search-container {
    transition: all 0.3s ease;
}

/* Focus States for Accessibility */
.kms-btn:focus,
.kms-search-input:focus {
    outline: 3px solid rgba(0, 200, 255, 0.5);
    outline-offset: 2px;
}

/* Storage Manager Styles */
.kms-storage-manager {
    max-width: 100%;
}

.kms-storage-stats {
    background: linear-gradient(135deg, var(--color-5), var(--color-3));
    border-radius: 10px;
    padding: 20px;
    margin-bottom: 20px;
    color: var(--text-color-2);
}

.kms-storage-stats h4 {
    margin: 0 0 15px 0;
    font-size: 1.2em;
    font-weight: 600;
}

.kms-storage-info {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 10px;
    margin-bottom: 15px;
}

.kms-storage-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: rgba(255, 255, 255, 0.1);
    padding: 8px 12px;
    border-radius: 6px;
    backdrop-filter: blur(10px);
}

.kms-storage-label {
    font-weight: 500;
    opacity: 0.9;
}

.kms-storage-value {
    font-weight: 600;
    font-size: 1.1em;
}

.kms-storage-bar {
    background: rgba(255, 255, 255, 0.2);
    border-radius: 10px;
    height: 12px;
    overflow: hidden;
    position: relative;
}

.kms-storage-progress {
    background: linear-gradient(90deg, var(--color-3), var(--color-4));
    height: 100%;
    border-radius: 10px;
    transition: width 0.5s ease;
    position: relative;
}

.kms-storage-progress::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    animation: shimmer 2s infinite;
}

@keyframes shimmer {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
}

.kms-storage-actions {
    background: var(--color-1);
    border-radius: 10px;
    padding: 20px;
    margin-bottom: 20px;
    color: var(--text-color-2);
}

.kms-storage-actions h4 {
    margin: 0 0 15px 0;
    font-size: 1.2em;
    font-weight: 600;
}

.kms-storage-buttons {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
}

.kms-storage-buttons .kms-btn {
    flex: 1;
    min-width: 120px;
    font-weight: 500;
    transition: all 0.3s ease;
}

.kms-storage-buttons .kms-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

/* Manage Dialog Header */
.kms-manage-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding: 15px;
    background: linear-gradient(135deg, var(--color-2), var(--color-4));
    border-radius: 10px;
    color: var(--text-color-2);
}

.kms-manage-info {
    margin: 0;
    font-size: 1.1em;
    font-weight: 500;
}

.kms-manage-actions {
    display: flex;
    gap: 10px;
}

.kms-manage-actions .kms-btn {
    font-weight: 500;
    transition: all 0.3s ease;
}

.kms-manage-actions .kms-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

/* Responsive Design for Storage Manager */
@media (max-width: 768px) {
    .kms-storage-info {
        grid-template-columns: 1fr;
    }
    
    .kms-storage-buttons {
        flex-direction: column;
    }
    
    .kms-storage-buttons .kms-btn {
        min-width: auto;
    }
    
    .kms-manage-header {
        flex-direction: column;
        gap: 15px;
        text-align: center;
    }
    
    .kms-manage-actions {
        justify-content: center;
    }
}

@media (max-width: 480px) {
    .kms-storage-item {
        flex-direction: column;
        gap: 5px;
        text-align: center;
    }
    
    .kms-manage-actions {
        flex-direction: column;
        width: 100%;
    }
}