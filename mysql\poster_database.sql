-- <PERSON>MS Poster Maker Database Schema
-- 海報製作器資料庫架構

-- 創建資料庫
CREATE DATABASE IF NOT EXISTS kms_poster_maker CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE kms_poster_maker;

-- 用戶表
CREATE TABLE IF NOT EXISTS users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    full_name VARCHAR(100),
    language ENUM('en', 'zh') DEFAULT 'zh',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    is_active BOOLEAN DEFAULT TRUE,
    reset_token VARCHAR(255) NULL,
    reset_token_expires TIMESTAMP NULL
);

-- 海報表
CREATE TABLE IF NOT EXISTS posters (
    id VARCHAR(50) PRIMARY KEY,
    user_id INT,
    name VARCHAR(255) NOT NULL,
    paper_size VARCHAR(20) DEFAULT 'A4',
    background_color VARCHAR(20) DEFAULT '#ffffff',
    poster_data LONGTEXT NOT NULL,
    thumbnail_path VARCHAR(500),
    file_size INT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    is_public BOOLEAN DEFAULT FALSE,
    tags TEXT,
    description TEXT,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_user_id (user_id),
    INDEX idx_created_at (created_at),
    INDEX idx_name (name)
);

-- 海報圖片檔案表
CREATE TABLE IF NOT EXISTS poster_images (
    id INT AUTO_INCREMENT PRIMARY KEY,
    poster_id VARCHAR(50),
    element_id VARCHAR(100),
    original_filename VARCHAR(255),
    server_filename VARCHAR(255) UNIQUE NOT NULL,
    file_path VARCHAR(500) NOT NULL,
    file_size INT DEFAULT 0,
    mime_type VARCHAR(100),
    width INT,
    height INT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (poster_id) REFERENCES posters(id) ON DELETE CASCADE,
    INDEX idx_poster_id (poster_id),
    INDEX idx_element_id (element_id)
);

-- 系統設定表
CREATE TABLE IF NOT EXISTS system_settings (
    id INT AUTO_INCREMENT PRIMARY KEY,
    setting_key VARCHAR(100) UNIQUE NOT NULL,
    setting_value TEXT,
    description TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- 插入預設系統設定
INSERT INTO system_settings (setting_key, setting_value, description) VALUES
('max_poster_size', '50', 'Maximum poster file size in MB'),
('max_image_size', '10', 'Maximum image file size in MB'),
('allowed_image_types', 'jpg,jpeg,png,gif,webp', 'Allowed image file types'),
('storage_path', 'uploads/', 'Base storage path for files'),
('enable_public_posters', '1', 'Enable public poster sharing'),
('max_posters_per_user', '100', 'Maximum posters per user')
ON DUPLICATE KEY UPDATE setting_value = VALUES(setting_value);

-- 創建用於測試的預設用戶（密碼：123456）
INSERT INTO users (username, email, password_hash, full_name, language) VALUES
('admin', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Administrator', 'zh'),
('demo', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Demo User', 'en')
ON DUPLICATE KEY UPDATE username = VALUES(username);