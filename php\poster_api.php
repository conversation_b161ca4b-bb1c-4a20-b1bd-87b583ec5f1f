<?php
/**
 * KMS Poster Maker - Poster API
 * 海報資料 API 處理
 */

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// 處理 OPTIONS 請求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

require_once 'config.php';

class PosterAPI {
    private $db;
    private $uploadDir;
    
    public function __construct() {
        $this->db = Database::getInstance()->getConnection();
        $this->uploadDir = dirname(__DIR__) . '/uploads/posters/';
        
        // 確保上傳目錄存在
        if (!is_dir($this->uploadDir)) {
            mkdir($this->uploadDir, 0755, true);
        }
    }
    
    /**
     * 處理 API 請求
     */
    public function handleRequest() {
        try {
            $method = $_SERVER['REQUEST_METHOD'];
            
            switch ($method) {
                case 'GET':
                    $this->handleGet();
                    break;
                case 'POST':
                    $this->handlePost();
                    break;
                case 'PUT':
                    $this->handlePut();
                    break;
                case 'DELETE':
                    $this->handleDelete();
                    break;
                default:
                    sendError('Method not allowed', 405);
            }
        } catch (Exception $e) {
            sendError('Internal server error: ' . $e->getMessage(), 500);
        }
    }
    
    /**
     * 處理 GET 請求
     */
    private function handleGet() {
        $action = $_GET['action'] ?? '';
        
        switch ($action) {
            case 'list':
                $this->listPosters();
                break;
            case 'get':
                $this->getPoster();
                break;
            case 'storage_stats':
                $this->getStorageStats();
                break;
            default:
                sendError('Invalid action', 400);
        }
    }
    
    /**
     * 處理 POST 請求 - 儲存海報
     */
    private function handlePost() {
        $input = json_decode(file_get_contents('php://input'), true);
        
        if (!$input || !isset($input['posterData'])) {
            sendError('Invalid poster data', 400);
            return;
        }
        
        $posterData = $input['posterData'];
        $userId = $input['userId'] ?? 1;
        
        // 驗證必要欄位
        if (!isset($posterData['name']) || !isset($posterData['elements'])) {
            sendError('Missing required fields', 400);
            return;
        }
        
        // 儲存海報資料
        $result = $this->savePoster($posterData, $userId);
        
        if ($result) {
            sendResponse($result, 200, 'Poster saved successfully');
        } else {
            sendError('Failed to save poster', 500);
        }
    }
    
    /**
     * 處理 PUT 請求 - 更新海報
     */
    private function handlePut() {
        $input = json_decode(file_get_contents('php://input'), true);
        
        if (!$input || !isset($input['id']) || !isset($input['posterData'])) {
            sendError('Invalid update data', 400);
            return;
        }
        
        $result = $this->updatePoster($input['id'], $input['posterData']);
        
        if ($result) {
            sendResponse(null, 200, 'Poster updated successfully');
        } else {
            sendError('Failed to update poster', 500);
        }
    }
    
    /**
     * 處理 DELETE 請求 - 刪除海報
     */
    private function handleDelete() {
        $id = $_GET['id'] ?? '';
        
        if (!$id) {
            sendError('Missing poster ID', 400);
            return;
        }
        
        $result = $this->deletePoster($id);
        
        if ($result) {
            sendResponse(null, 200, 'Poster deleted successfully');
        } else {
            sendError('Failed to delete poster', 500);
        }
    }
    
    /**
     * 儲存海報資料
     */
    private function savePoster($posterData, $userId) {
        try {
            // 生成檔案名稱
            $fileName = $posterData['id'] . '.json';
            $filePath = $this->uploadDir . $fileName;
            
            // 儲存 JSON 檔案
            $jsonData = json_encode($posterData, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
            $fileSize = file_put_contents($filePath, $jsonData);
            
            if ($fileSize === false) {
                throw new Exception('Failed to write poster file');
            }
            
            // 儲存到資料庫
            $sql = "INSERT INTO posters (id, user_id, name, file_size, poster_data, created_at, updated_at) 
                    VALUES (?, ?, ?, ?, ?, NOW(), NOW())
                    ON DUPLICATE KEY UPDATE 
                    name = VALUES(name), 
                    file_size = VALUES(file_size), 
                    poster_data = VALUES(poster_data), 
                    updated_at = NOW()";
            
            $stmt = $this->db->prepare($sql);
            $stmt->execute([
                $posterData['id'], 
                $userId, 
                $posterData['name'], 
                $fileSize, 
                $jsonData
            ]);
            
            if ($stmt->rowCount() >= 0) {
                return [
                    'id' => $posterData['id'],
                    'fileName' => $fileName,
                    'fileSize' => formatFileSize($fileSize),
                    'filePath' => $filePath
                ];
            } else {
                throw new Exception('Database insert failed: ' . $stmt->error);
            }
            
        } catch (Exception $e) {
            error_log('Error saving poster: ' . $e->getMessage());
            return false;
        }
    }
    
    /**
     * 取得海報列表
     */
    private function listPosters() {
        $userId = $_GET['userId'] ?? 1;
        $page = max(1, intval($_GET['page'] ?? 1));
        $limit = max(1, min(100, intval($_GET['limit'] ?? 20)));
        $offset = ($page - 1) * $limit;
        
        try {
            // 取得總數
            $countSql = "SELECT COUNT(*) as total FROM posters WHERE user_id = ?";
            $countStmt = $this->db->prepare($countSql);
            $countStmt->execute([$userId]);
            $totalResult = $countStmt->fetch();
            $total = $totalResult['total'];
            
            // 取得海報列表
            $sql = "SELECT id, name, file_size, created_at, updated_at 
                    FROM posters 
                    WHERE user_id = ? 
                    ORDER BY updated_at DESC 
                    LIMIT ? OFFSET ?";
            
            $stmt = $this->db->prepare($sql);
            $stmt->execute([$userId, $limit, $offset]);
            
            $posters = $stmt->fetchAll();
            
            sendResponse([
                'posters' => $posters,
                'pagination' => [
                    'page' => $page,
                    'limit' => $limit,
                    'total' => $total,
                    'totalPages' => ceil($total / $limit)
                ]
            ], 200, 'Posters retrieved successfully');
            
        } catch (Exception $e) {
            sendError('Failed to retrieve posters: ' . $e->getMessage(), 500);
        }
    }
    
    /**
     * 取得單一海報
     */
    private function getPoster() {
        $id = $_GET['id'] ?? '';
        
        if (!$id) {
            sendError('Missing poster ID', 400);
            return;
        }
        
        try {
            $sql = "SELECT * FROM posters WHERE id = ?";
            $stmt = $this->db->prepare($sql);
            $stmt->execute([$id]);
            
            if ($row = $stmt->fetch()) {
                // 解析 JSON 資料
                $posterData = json_decode($row['poster_data'], true);
                
                sendResponse([
                    'id' => $row['id'],
                    'name' => $row['name'],
                    'file_size' => $row['file_size'],
                    'file_size_formatted' => formatFileSize($row['file_size']),
                    'created_at' => $row['created_at'],
                    'updated_at' => $row['updated_at'],
                    'poster_data' => $posterData
                ], 200, 'Poster retrieved successfully');
            } else {
                sendError('Poster not found', 404);
            }
            
        } catch (Exception $e) {
            sendError('Failed to retrieve poster: ' . $e->getMessage(), 500);
        }
    }
    
    /**
     * 取得儲存統計
     */
    private function getStorageStats() {
        $userId = $_GET['userId'] ?? 1;
        
        try {
            // 取得海報統計
            $posterSql = "SELECT COUNT(*) as poster_count, COALESCE(SUM(file_size), 0) as total_size 
                           FROM posters WHERE user_id = ?";
            $posterStmt = $this->db->prepare($posterSql);
            $posterStmt->execute([$userId]);
            $posterStats = $posterStmt->fetch();
            
            // 取得圖片統計
            $imageSql = "SELECT COUNT(*) as image_count, COALESCE(SUM(pi.file_size), 0) as total_size 
                        FROM poster_images pi 
                        JOIN posters p ON pi.poster_id = p.id 
                        WHERE p.user_id = ?";
            $imageStmt = $this->db->prepare($imageSql);
            $imageStmt->execute([$userId]);
            $imageStats = $imageStmt->fetch();
            
            $totalSize = ($posterStats['total_size'] ?? 0) + ($imageStats['total_size'] ?? 0);
            
            sendResponse([
                'poster_count' => $posterStats['poster_count'] ?? 0,
                'image_count' => $imageStats['image_count'] ?? 0,
                'total_size' => $totalSize,
                'total_size_formatted' => formatFileSize($totalSize),
                'poster_size' => $posterStats['total_size'] ?? 0,
                'poster_size_formatted' => formatFileSize($posterStats['total_size'] ?? 0),
                'image_size' => $imageStats['total_size'] ?? 0,
                'image_size_formatted' => formatFileSize($imageStats['total_size'] ?? 0)
            ], 200, 'Storage stats retrieved successfully');
            
        } catch (Exception $e) {
            sendError('Failed to get storage stats: ' . $e->getMessage(), 500);
        }
    }
    
    /**
     * 更新海報
     */
    private function updatePoster($id, $posterData) {
        try {
            // 更新檔案
            $fileName = $id . '.json';
            $filePath = $this->uploadDir . $fileName;
            
            $jsonData = json_encode($posterData, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
            $fileSize = file_put_contents($filePath, $jsonData);
            
            if ($fileSize === false) {
                throw new Exception('Failed to update poster file');
            }
            
            // 更新資料庫
            $sql = "UPDATE posters SET name = ?, file_size = ?, poster_data = ?, updated_at = NOW() WHERE id = ?";
            $stmt = $this->db->prepare($sql);
            $stmt->bind_param('siss', $posterData['name'], $fileSize, $jsonData, $id);
            
            return $stmt->execute();
            
        } catch (Exception $e) {
            error_log('Error updating poster: ' . $e->getMessage());
            return false;
        }
    }
    
    /**
     * 刪除海報
     */
    private function deletePoster($id) {
        try {
            // 構建檔案路徑
            $filePath = $this->uploadDir . $id . '.json';
            
            // 刪除檔案（如果存在）
            if (file_exists($filePath)) {
                unlink($filePath);
            }
            
            // 刪除資料庫記錄
            $deleteSql = "DELETE FROM posters WHERE id = ?";
            $deleteStmt = $this->db->prepare($deleteSql);
            $deleteStmt->execute([$id]);
            
            return $deleteStmt->rowCount() > 0;
            
        } catch (Exception $e) {
            error_log('Error deleting poster: ' . $e->getMessage());
            return false;
        }
    }
}

// 執行 API
$api = new PosterAPI();
$api->handleRequest();
?>