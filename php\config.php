<?php
/**
 * K<PERSON> Poster Maker - Database Configuration
 * 資料庫配置檔案
 */

// 設定PHP上傳限制 - 設定大檔案上傳支援
ini_set('upload_max_filesize', '500M');  // 500MB
ini_set('post_max_size', '500M');        // 500MB
ini_set('max_input_time', 300);          // 5分鐘
ini_set('max_execution_time', 300);      // 5分鐘
ini_set('memory_limit', '512M');         // 512MB

// 資料庫配置
define('DB_HOST', 'localhost');
define('DB_NAME', 'kms_poster_maker');
define('DB_USER', 'root');
define('DB_PASS', '');
define('DB_CHARSET', 'utf8mb4');

// 檔案上傳配置
define('UPLOAD_PATH', __DIR__ . '/../uploads/');
define('POSTER_UPLOAD_PATH', UPLOAD_PATH . 'posters/');
define('IMAGE_UPLOAD_PATH', UPLOAD_PATH . 'images/');
define('THUMBNAIL_UPLOAD_PATH', UPLOAD_PATH . 'thumbnails/');

// 檔案大小限制 - 完全移除限制
// define('MAX_POSTER_SIZE', PHP_INT_MAX); // 無限制
// define('MAX_IMAGE_SIZE', PHP_INT_MAX);  // 無限制

// 允許的檔案類型
define('ALLOWED_IMAGE_TYPES', ['jpg', 'jpeg', 'png', 'gif', 'webp', 'svg']);

// 安全設定
define('JWT_SECRET', 'your-secret-key-here-change-in-production');
define('SESSION_TIMEOUT', 3600); // 1小時

// 語言設定
define('DEFAULT_LANGUAGE', 'zh');
define('SUPPORTED_LANGUAGES', ['en', 'zh']);

// 錯誤報告
error_reporting(E_ALL);
ini_set('display_errors', 1);

// 設定時區
date_default_timezone_set('Asia/Taipei');

// 創建上傳目錄
$directories = [
    UPLOAD_PATH,
    POSTER_UPLOAD_PATH,
    IMAGE_UPLOAD_PATH,
    THUMBNAIL_UPLOAD_PATH
];

foreach ($directories as $dir) {
    if (!file_exists($dir)) {
        mkdir($dir, 0755, true);
    }
}

/**
 * 資料庫連接類
 */
class Database {
    private static $instance = null;
    private $connection;
    
    private function __construct() {
        try {
            $dsn = "mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=" . DB_CHARSET;
            $this->connection = new PDO($dsn, DB_USER, DB_PASS, [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                PDO::ATTR_EMULATE_PREPARES => false
            ]);
        } catch (PDOException $e) {
            die("資料庫連接失敗: " . $e->getMessage());
        }
    }
    
    public static function getInstance() {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    public function getConnection() {
        return $this->connection;
    }
}

/**
 * 回應處理函數
 */
function sendResponse($data, $status = 200, $message = '') {
    http_response_code($status);
    header('Content-Type: application/json; charset=utf-8');
    header('Access-Control-Allow-Origin: *');
    header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
    header('Access-Control-Allow-Headers: Content-Type, Authorization');
    
    echo json_encode([
        'status' => $status,
        'message' => $message,
        'data' => $data
    ], JSON_UNESCAPED_UNICODE);
    exit;
}

/**
 * 錯誤處理函數
 */
function sendError($message, $status = 400, $details = null) {
    sendResponse($details, $status, $message);
}

/**
 * 驗證檔案類型
 */
function validateImageType($filename) {
    $extension = strtolower(pathinfo($filename, PATHINFO_EXTENSION));
    return in_array($extension, ALLOWED_IMAGE_TYPES);
}

/**
 * 生成唯一檔案名
 */
function generateUniqueFilename($originalFilename) {
    $extension = pathinfo($originalFilename, PATHINFO_EXTENSION);
    return uniqid() . '_' . time() . '.' . $extension;
}

/**
 * 格式化檔案大小
 */
function formatFileSize($bytes) {
    $units = ['B', 'KB', 'MB', 'GB'];
    $bytes = max($bytes, 0);
    $pow = floor(($bytes ? log($bytes) : 0) / log(1024));
    $pow = min($pow, count($units) - 1);
    
    $bytes /= (1 << (10 * $pow));
    
    return round($bytes, 2) . ' ' . $units[$pow];
}
?>