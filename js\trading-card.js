/**
 * KMS Poster Maker - Trading Card Module
 * 交易卡模塊
 */

class KMSTradingCard {
    constructor(posterMaker) {
        this.posterMaker = posterMaker;
        this.tradingCards = [];
        this.currentLayout = null;
        this.editMode = 'single'; // 'single' or 'sync'
        this.syncEnabled = false;
        this.init();
    }
    
    init() {
        this.setupEventListeners();
        this.setupPaperSizeListener();
    }
    
    setupEventListeners() {
        // 生成交易卡按鈕
        const generateBtn = document.getElementById('generateTradingCardsBtn');
        if (generateBtn) {
            generateBtn.addEventListener('click', () => this.generateTradingCards());
        }

        // 套用到所有卡片按鈕（單張模式）
        const applyToAllBtn = document.getElementById('applyToAllCardsBtn');
        if (applyToAllBtn) {
            applyToAllBtn.addEventListener('click', () => this.applyToAllCards());
        }

        // 同步按鈕（多張同步模式）
        const applySyncBtn = document.getElementById('applySyncBtn');
        if (applySyncBtn) {
            applySyncBtn.addEventListener('click', () => this.syncCard1ToAll());
        }

        // 編輯模式切換
        const singleEditMode = document.getElementById('singleEditMode');
        const syncEditMode = document.getElementById('syncEditMode');

        if (singleEditMode) {
            singleEditMode.addEventListener('change', () => {
                if (singleEditMode.checked) {
                    this.setEditMode('single');
                }
            });
        }

        if (syncEditMode) {
            syncEditMode.addEventListener('change', () => {
                if (syncEditMode.checked) {
                    this.setEditMode('sync');
                }
            });
        }
        
        // 紙張類型選擇
        const paperSelect = document.getElementById('tradingCardPaper');
        if (paperSelect) {
            paperSelect.addEventListener('change', () => this.updateLayout());
        }
    }
    
    setupPaperSizeListener() {
        // 監聽紙張尺寸變化
        document.querySelectorAll('.kms-paper-option').forEach(option => {
            option.addEventListener('click', (e) => {
                const size = e.currentTarget.dataset.size;
                this.handlePaperSizeChange(size);
            });
        });
    }
    
    handlePaperSizeChange(size) {
        const tradingCardSection = document.getElementById('tradingCardLayoutSection');

        if (size === 'trading-card') {
            // 顯示交易卡佈局選項
            tradingCardSection.style.display = 'block';
            
            // 保存現有元素
            const existingElements = this.saveExistingElements();
            
            this.setupTradingCardCanvas();

            // 自動生成 Letter 佈局的預覽網格
            setTimeout(() => {
                const paperSelect = document.getElementById('tradingCardPaper');
                if (paperSelect) {
                    paperSelect.value = 'letter';
                    this.generateTradingCards();
                    
                    // 同步現有元素到Trading Card
                    this.syncExistingElementsToTradingCards(existingElements);
                }
            }, 100);
        } else {
            // 隱藏交易卡佈局選項，但不清除現有元素
            tradingCardSection.style.display = 'none';
            this.clearTradingCards();
            
            // 恢復普通畫布模式，保留現有元素
            const canvas = document.getElementById('posterCanvas');
            if (canvas) {
                // 移除trading card相關的類名
                canvas.classList.remove('kms-canvas-letter', 'kms-canvas-4x6');
                
                // 恢復所有現有元素的交互功能
                const existingElements = canvas.querySelectorAll('.kms-canvas-element');
                existingElements.forEach(element => {
                    // 確保元素有正確的類名
                    if (!element.classList.contains('kms-canvas-element')) {
                        element.classList.add('kms-canvas-element');
                    }
                    
                    // 重新設置交互功能
                    if (this.posterMaker && this.posterMaker.setupElementInteraction) {
                        this.posterMaker.setupElementInteraction(element);
                    }
                });
            }
        }
    }
    
    // 保存現有元素
    saveExistingElements() {
        const canvas = document.getElementById('posterCanvas');
        if (!canvas) return [];
        
        const elements = [];
        const canvasElements = canvas.querySelectorAll('.kms-canvas-element');
        
        canvasElements.forEach(element => {
            const elementData = {
                type: this.getElementType(element),
                element: element.cloneNode(true),
                styles: window.getComputedStyle(element),
                position: {
                    left: element.style.left,
                    top: element.style.top,
                    width: element.style.width,
                    height: element.style.height
                }
            };
            elements.push(elementData);
        });
        
        return elements;
    }
    
    // 獲取元素類型
    getElementType(element) {
        if (element.classList.contains('kms-text-element')) return 'text';
        if (element.classList.contains('kms-image-element')) return 'image';
        if (element.classList.contains('kms-qr-element')) return 'qr';
        return 'unknown';
    }
    
    // 同步現有元素到Trading Cards
    syncExistingElementsToTradingCards(existingElements) {
        if (!existingElements || existingElements.length === 0) return;
        
        // 等待Trading Cards生成完成
        setTimeout(() => {
            const card1 = this.getCard1();
            if (!card1) return;
            
            const card1Content = card1.querySelector('.kms-trading-card-content');
            if (!card1Content) return;
            
            // 將每個元素添加到Card 1
            existingElements.forEach(elementData => {
                const clonedElement = this.createTradingCardElementFromData(elementData, card1Content);
                if (clonedElement) {
                    card1Content.appendChild(clonedElement);
                }
            });
            
            // 如果是同步模式，同步到所有卡片
            if (this.editMode === 'sync') {
                this.syncCard1ToAll();
            }
        }, 200);
    }
    
    // 從元素數據創建Trading Card元素
    createTradingCardElementFromData(elementData, container) {
        const element = elementData.element.cloneNode(true);
        
        // 移除canvas相關類名，添加trading card類名
        element.classList.remove('kms-canvas-element', 'selected');
        element.classList.add('kms-trading-card-element');
        
        // 根據類型添加特定類名
        if (elementData.type === 'text') {
            element.classList.add('kms-trading-card-text-element');
        } else if (elementData.type === 'image') {
            element.classList.add('kms-trading-card-image-element');
        } else if (elementData.type === 'qr') {
            element.classList.add('kms-trading-card-qr-element');
        }
        
        // 移除舊的resize handles
        const oldHandles = element.querySelectorAll('.kms-resize-handle');
        oldHandles.forEach(handle => handle.remove());
        
        // 縮放元素以適應Trading Card
        this.scaleElementForTradingCard(element, elementData, container);
        
        // 添加新的resize handles和交互
        this.addResizeHandlesToElement(element);
        this.setupTradingCardElementInteraction(element);
        
        return element;
    }
    
    // 縮放元素以適應Trading Card
    scaleElementForTradingCard(element, elementData, container) {
        const canvas = document.getElementById('posterCanvas');
        const canvasWidth = canvas ? canvas.offsetWidth : 800;
        const canvasHeight = canvas ? canvas.offsetHeight : 600;
        const cardWidth = container.offsetWidth || 240;
        const cardHeight = container.offsetHeight || 336;
        
        // 計算縮放比例
        const scaleX = cardWidth / canvasWidth;
        const scaleY = cardHeight / canvasHeight;
        const scale = Math.min(scaleX, scaleY, 0.8); // 限制最大縮放為80%
        
        // 應用縮放
        const originalWidth = parseInt(elementData.position.width) || 100;
        const originalHeight = parseInt(elementData.position.height) || 50;
        const originalLeft = parseInt(elementData.position.left) || 0;
        const originalTop = parseInt(elementData.position.top) || 0;
        
        const newWidth = originalWidth * scale;
        const newHeight = originalHeight * scale;
        const newLeft = originalLeft * scale;
        const newTop = originalTop * scale;
        
        element.style.width = newWidth + 'px';
        element.style.height = newHeight + 'px';
        element.style.left = Math.min(newLeft, cardWidth - newWidth) + 'px';
        element.style.top = Math.min(newTop, cardHeight - newHeight) + 'px';
        
        // 對於圖片元素，調整內部img標籤
        const img = element.querySelector('img');
        if (img) {
            img.style.width = '100%';
            img.style.height = '100%';
            img.style.objectFit = 'contain';
        }
    }
    
    // 設置編輯模式
    setEditMode(mode) {
        this.editMode = mode;
        this.updateEditModeUI();

        if (mode === 'sync') {
            this.enableSyncMode();
        } else {
            this.disableSyncMode();
        }
    }

    // 更新編輯模式界面
    updateEditModeUI() {
        const singleModeControls = document.getElementById('singleModeControls');
        const syncModeControls = document.getElementById('syncModeControls');

        if (this.editMode === 'sync') {
            if (singleModeControls) singleModeControls.style.display = 'none';
            if (syncModeControls) syncModeControls.style.display = 'block';
        } else {
            if (singleModeControls) singleModeControls.style.display = 'block';
            if (syncModeControls) syncModeControls.style.display = 'none';
        }
    }

    // 啟用同步模式
    enableSyncMode() {
        this.syncEnabled = true;
        // 高亮 Card 1
        this.highlightCard1();
        // 設置 Card 1 的監聽器
        this.setupCard1Listeners();
    }

    // 禁用同步模式
    disableSyncMode() {
        this.syncEnabled = false;
        // 移除 Card 1 高亮
        this.removeCard1Highlight();
        // 移除 Card 1 的特殊監聽器
        this.removeCard1Listeners();
    }

    // 高亮 Card 1
    highlightCard1() {
        const card1 = this.getCard1();
        if (card1) {
            card1.classList.add('kms-sync-master-card');
            card1.style.borderColor = '#007bff';
            card1.style.borderWidth = '3px';
            card1.style.boxShadow = '0 0 10px rgba(0, 123, 255, 0.5)';
        }
    }

    // 移除 Card 1 高亮
    removeCard1Highlight() {
        const card1 = this.getCard1();
        if (card1) {
            card1.classList.remove('kms-sync-master-card');
            card1.style.borderColor = '';
            card1.style.borderWidth = '';
            card1.style.boxShadow = '';
        }
    }

    // 獲取 Card 1
    getCard1() {
        return this.tradingCards.find(card =>
            card.dataset.cardNumber === '1' ||
            card.querySelector('.kms-trading-card-label')?.textContent.includes('Card 1')
        );
    }

    // 設置 Card 1 的監聽器
    setupCard1Listeners() {
        const card1 = this.getCard1();
        if (!card1) return;

        const cardContent = card1.querySelector('.kms-trading-card-content');
        if (!cardContent) return;

        // 監聽 Card 1 內容變化
        this.card1Observer = new MutationObserver((mutations) => {
            if (this.syncEnabled) {
                this.syncCard1ToAll();
            }
        });

        this.card1Observer.observe(cardContent, {
            childList: true,
            subtree: true,
            attributes: true,
            attributeOldValue: true,
            characterData: true
        });
    }

    // 移除 Card 1 的監聽器
    removeCard1Listeners() {
        if (this.card1Observer) {
            this.card1Observer.disconnect();
            this.card1Observer = null;
        }
    }

    // 檢查是否應該允許在畫布上編輯
    shouldAllowCanvasEdit() {
        // 如果不是 trading card 模式，允許編輯
        const tradingCardSection = document.getElementById('tradingCardLayoutSection');
        if (!tradingCardSection || tradingCardSection.style.display === 'none') {
            return true;
        }

        // 如果是 trading card 模式，不允許在畫布上編輯
        return false;
    }

    // 檢查是否應該允許在 Card 1 中編輯
    shouldAllowCard1Edit() {
        // 如果不是 trading card 模式，不允許
        const tradingCardSection = document.getElementById('tradingCardLayoutSection');
        if (!tradingCardSection || tradingCardSection.style.display === 'none') {
            return false;
        }

        // 如果是 trading card 模式，允許在 Card 1 中編輯
        return true;
    }

    // 獲取應該添加元素的容器
    getElementContainer() {
        const tradingCardSection = document.getElementById('tradingCardLayoutSection');
        
        // 如果在Trading Card模式下
        if (tradingCardSection && tradingCardSection.style.display !== 'none') {
            // 優先使用選中的卡片
            if (this.selectedCard) {
                const contentArea = this.selectedCard.querySelector('.kms-trading-card-content');
                if (contentArea) {
                    return contentArea;
                }
            }
            
            // 如果沒有選中的卡片，使用Card 1
            const card1 = this.getCard1();
            if (card1) {
                const contentArea = card1.querySelector('.kms-trading-card-content');
                if (contentArea) {
                    // 自動選中Card 1
                    this.selectCard(card1);
                    return contentArea;
                }
            }
            
            // 如果沒有任何卡片，返回null並顯示提示
            this.showMessage('請先生成Trading Card佈局');
            return null;
        }

        // 如果不在Trading Card模式下，返回普通畫布
        return document.getElementById('posterCanvas');
    }
    
    setupTradingCardCanvas() {
        const canvas = document.getElementById('posterCanvas');
        if (!canvas) return;
        
        // 設置畫布為 Letter 尺寸作為默認
        canvas.style.width = '816px';
        canvas.style.height = '1056px';
        canvas.classList.remove('kms-canvas-4x6', 'kms-canvas-trading-card');
        canvas.classList.add('kms-canvas-letter', 'kms-canvas-trading-card-layout');
        
        // 清除現有內容
        this.clearCanvas();
    }
    
    generateTradingCards() {
        const paperType = document.getElementById('tradingCardPaper').value;
        const canvas = document.getElementById('posterCanvas');
        if (!canvas) return;
        
        // 清除現有的交易卡
        this.clearTradingCards();
        
        // 根據紙張類型設置畫布
        this.setupCanvasForPaper(paperType);
        
        // 生成交易卡佈局
        if (paperType === 'letter') {
            this.generateLetterLayout();
        } else if (paperType === '4x6') {
            this.generate4x6Layout();
        }

        this.currentLayout = paperType;

        // 設置編輯模式（延遲執行以確保卡片已生成）
        setTimeout(() => {
            this.updateEditModeUI();
            if (this.editMode === 'sync') {
                this.enableSyncMode();
            }
        }, 100);
    }
    
    setupCanvasForPaper(paperType) {
        const canvas = document.getElementById('posterCanvas');

        if (paperType === 'letter') {
            canvas.style.width = '816px';
            canvas.style.height = '1056px';
            canvas.classList.remove('kms-canvas-4x6');
            canvas.classList.add('kms-canvas-letter');
        } else if (paperType === '4x6') {
            // 4x6 畫布旋轉 90 度：從 384x576 變成 576x384
            canvas.style.width = '576px';
            canvas.style.height = '384px';
            canvas.classList.remove('kms-canvas-letter');
            canvas.classList.add('kms-canvas-4x6');
        }
    }
    
    generateLetterLayout() {
        // Letter 紙張：8.5" x 11" = 816px x 1056px
        // Trading Card：2.5" x 3.5" = 240px x 336px
        // 3x3 佈局，每張卡片之間有間距
        
        const cardWidth = 240;
        const cardHeight = 336;
        const cols = 3;
        const rows = 3;
        const marginX = (816 - (cols * cardWidth)) / (cols + 1);
        const marginY = (1056 - (rows * cardHeight)) / (rows + 1);
        
        for (let row = 0; row < rows; row++) {
            for (let col = 0; col < cols; col++) {
                const x = marginX + col * (cardWidth + marginX);
                const y = marginY + row * (cardHeight + marginY);
                
                this.createTradingCard(x, y, cardWidth, cardHeight, row * cols + col + 1);
            }
        }
    }
    
    generate4x6Layout() {
        // 4x6 紙張：橫向6" x 豎向4" = 576px x 384px
        // Trading Card 標準：2.5" x 3.5" = 240px x 336px
        // 2x1 佈局（水平排列兩張卡片）

        const cardWidth = 240; // 2.5"
        const cardHeight = 336; // 3.5"
        const cols = 2;
        const rows = 1;
        
        // 計算邊距以確保卡片居中且不被裁切
        const totalCardsWidth = cols * cardWidth;
        const marginX = (576 - totalCardsWidth) / (cols + 1); // 平均分配水平邊距
        const marginY = (384 - cardHeight) / 2; // 垂直居中

        for (let col = 0; col < cols; col++) {
            const x = marginX + col * (cardWidth + marginX);
            const y = marginY;

            this.createTradingCard(x, y, cardWidth, cardHeight, col + 1);
        }
    }
    
    createTradingCard(x, y, width, height, cardNumber) {
        const canvas = document.getElementById('posterCanvas');
        const cardContainer = document.createElement('div');
        
        cardContainer.className = 'kms-trading-card';
        cardContainer.dataset.cardNumber = cardNumber;
        cardContainer.style.cssText = `
            position: absolute;
            left: ${x}px;
            top: ${y}px;
            width: ${width}px;
            height: ${height}px;
            border: 2px dashed var(--border-color-1);
            background: rgba(255, 255, 255, 0.9);
            border-radius: 8px;
            box-sizing: border-box;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            font-family: Arial, sans-serif;
            color: var(--text-color-1);
        `;
        
        // 添加卡片標籤
        const cardLabel = document.createElement('div');
        cardLabel.className = 'kms-trading-card-label';
        cardLabel.textContent = `Card ${cardNumber}`;
        cardLabel.style.cssText = `
            position: absolute;
            top: 10px;
            left: 10px;
            background: var(--color-1);
            color: var(--text-color-2);
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: bold;
        `;
        
        // 添加示例內容區域
        const contentArea = document.createElement('div');
        contentArea.className = 'kms-trading-card-content';
        contentArea.style.cssText = `
            width: 100%;
            height: 100%;
            border: 1px solid white;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 14px;
            color: var(--text-color-3);
            text-align: center;
            position: relative;
            overflow: hidden;
            box-sizing: border-box;
        `;
        // 移除 "Add Content" 字樣，保持空白
        contentArea.innerHTML = ``;
        
        cardContainer.appendChild(cardLabel);
        cardContainer.appendChild(contentArea);
        canvas.appendChild(cardContainer);
        
        this.tradingCards.push(cardContainer);
        
        // 添加點擊事件來選擇卡片
        cardContainer.addEventListener('click', (e) => {
            e.stopPropagation();
            this.selectCard(cardContainer);
        });
    }
    
    selectCard(card) {
        // 移除其他卡片的選中狀態
        this.tradingCards.forEach(c => c.classList.remove('selected'));
        
        // 選中當前卡片
        card.classList.add('selected');
        card.style.borderColor = 'var(--color-2)';
        card.style.borderStyle = 'solid';
        
        // 更新選中的卡片
        this.selectedCard = card;
    }
    
    applyToAllCards() {
        // 在單張編輯模式下，從 Card 1 複製內容到其他卡片
        const card1 = this.getCard1();
        if (!card1) {
            this.showMessage('找不到 Card 1');
            return;
        }

        const card1Content = card1.querySelector('.kms-trading-card-content');
        if (!card1Content) {
            this.showMessage('Card 1 沒有內容區域');
            return;
        }

        const card1Elements = card1Content.querySelectorAll('.kms-trading-card-text-element, .kms-trading-card-image-element, .kms-trading-card-qr-element');

        if (card1Elements.length === 0) {
            this.showMessage('請先在 Card 1 中添加內容（文字、圖片或 QR Code）');
            return;
        }

        // 獲取 Card 1 的尺寸作為基準
        const card1Rect = card1Content.getBoundingClientRect();
        const card1Width = card1Rect.width;
        const card1Height = card1Rect.height;

        // 複製到其他卡片
        this.tradingCards.forEach((card, index) => {
            // 跳過 Card 1 本身
            if (card === card1) return;

            const cardContent = card.querySelector('.kms-trading-card-content');
            if (cardContent) {
                // 清除現有內容
                cardContent.innerHTML = '';

                // 獲取目標卡片的尺寸
                const cardRect = cardContent.getBoundingClientRect();
                const cardWidth = cardRect.width;
                const cardHeight = cardRect.height;

                // 計算縮放比例（基於 Card 1 的實際大小）
                const scaleX = cardWidth / card1Width;
                const scaleY = cardHeight / card1Height;
                const scale = Math.min(scaleX, scaleY);

                // 克隆並縮放 Card 1 的元素
                card1Elements.forEach(element => {
                    const clonedElement = this.cloneCard1ElementForCard(element, scale);
                    if (clonedElement) {
                        cardContent.appendChild(clonedElement);
                    }
                });
            }
        });

        this.showMessage(`已套用 Card 1 內容到所有 ${this.tradingCards.length - 1} 張卡片`);
    }

    // 同步 Card 1 到所有卡片（基於 Card 1 的實際大小縮放）
    syncCard1ToAll() {
        const card1 = this.getCard1();
        if (!card1) {
            this.showMessage('找不到 Card 1');
            return;
        }

        const card1Content = card1.querySelector('.kms-trading-card-content');
        if (!card1Content) {
            this.showMessage('Card 1 沒有內容區域');
            return;
        }

        const card1Elements = card1Content.querySelectorAll('.kms-trading-card-text-element, .kms-trading-card-image-element, .kms-trading-card-qr-element');

        if (card1Elements.length === 0) {
            this.showMessage('Card 1 沒有內容可同步');
            return;
        }

        // 獲取 Card 1 的尺寸作為基準
        const card1Rect = card1Content.getBoundingClientRect();
        const card1Width = card1Rect.width;
        const card1Height = card1Rect.height;

        // 同步到其他卡片
        this.tradingCards.forEach((card, index) => {
            // 跳過 Card 1 本身
            if (card === card1) return;

            const cardContent = card.querySelector('.kms-trading-card-content');
            if (cardContent) {
                // 清除現有內容
                cardContent.innerHTML = '';

                // 獲取目標卡片的尺寸
                const cardRect = cardContent.getBoundingClientRect();
                const cardWidth = cardRect.width;
                const cardHeight = cardRect.height;

                // 計算縮放比例（基於 Card 1 的實際大小）
                const scaleX = cardWidth / card1Width;
                const scaleY = cardHeight / card1Height;
                const scale = Math.min(scaleX, scaleY);

                // 克隆並縮放 Card 1 的元素
                card1Elements.forEach(element => {
                    const clonedElement = this.cloneCard1ElementForCard(element, scale);
                    if (clonedElement) {
                        cardContent.appendChild(clonedElement);
                    }
                });
            }
        });

        this.showMessage(`已同步 Card 1 到所有 ${this.tradingCards.length - 1} 張卡片`);
    }

    // 克隆 Card 1 的元素到其他卡片（基於實際大小縮放）
    cloneCard1ElementForCard(element, scale) {
        const clone = element.cloneNode(true);

        // 獲取原始元素的樣式和位置
        const computedStyle = window.getComputedStyle(element);
        const originalLeft = parseFloat(element.style.left) || 0;
        const originalTop = parseFloat(element.style.top) || 0;
        const originalWidth = parseFloat(element.style.width) || parseFloat(computedStyle.width) || 0;
        const originalHeight = parseFloat(element.style.height) || parseFloat(computedStyle.height) || 0;

        // 應用縮放
        clone.style.left = (originalLeft * scale) + 'px';
        clone.style.top = (originalTop * scale) + 'px';
        clone.style.width = (originalWidth * scale) + 'px';
        clone.style.height = (originalHeight * scale) + 'px';

        // 處理文字元素的字體大小
        if (element.classList.contains('kms-trading-card-text-element')) {
            const originalFontSize = parseFloat(computedStyle.fontSize) || 16;
            const newFontSize = Math.max(8, Math.min(24, originalFontSize * scale));
            clone.style.fontSize = newFontSize + 'px';
        }

        // 處理圖片元素
        if (element.classList.contains('kms-trading-card-image-element')) {
            const originalImg = element.querySelector('img');
            const cloneImg = clone.querySelector('img');
            if (originalImg && cloneImg) {
                // 確保圖片源正確複製
                cloneImg.src = originalImg.src;
                cloneImg.alt = originalImg.alt || '';
                
                // 縮放圖片尺寸
                const originalImgWidth = parseFloat(originalImg.style.width) || originalImg.offsetWidth;
                const originalImgHeight = parseFloat(originalImg.style.height) || originalImg.offsetHeight;
                cloneImg.style.width = (originalImgWidth * scale) + 'px';
                cloneImg.style.height = (originalImgHeight * scale) + 'px';
                cloneImg.style.objectFit = 'contain';
                cloneImg.style.display = 'block';
                
                // 確保圖片容器樣式正確
                clone.style.overflow = 'hidden';
                clone.style.display = 'flex';
                clone.style.alignItems = 'center';
                clone.style.justifyContent = 'center';
            }
        }

        // 處理 QR Code 元素
        if (element.classList.contains('kms-trading-card-qr-element')) {
            const img = clone.querySelector('img');
            if (img) {
                const originalImgWidth = parseFloat(img.style.width) || img.offsetWidth;
                const originalImgHeight = parseFloat(img.style.height) || img.offsetHeight;
                img.style.width = (originalImgWidth * scale) + 'px';
                img.style.height = (originalImgHeight * scale) + 'px';
            }
        }

        // 確保元素不可編輯和拖拽
        clone.contentEditable = false;
        clone.draggable = false;

        // 移除任何控制元素
        const handles = clone.querySelectorAll('.kms-resize-handle, .kms-element-controls');
        handles.forEach(handle => handle.remove());

        return clone;
    }
    
    cloneElementForCard(element, cardContent = null) {
        const clone = element.cloneNode(true);

        // Remove canvas-specific classes and add card-specific classes
        clone.classList.remove('kms-canvas-element', 'selected', 'dragging');
        clone.classList.add('kms-trading-card-element');

        // Store original dimensions and properties
        const originalData = this.getElementOriginalData(element);
        clone.dataset.originalData = JSON.stringify(originalData);

        // 確保圖片元素的 src 正確複製
        if (element.classList.contains('kms-image-element')) {
            const originalImg = element.querySelector('img');
            const clonedImg = clone.querySelector('img');
            if (originalImg && clonedImg && originalImg.src) {
                clonedImg.src = originalImg.src;
                clonedImg.alt = originalImg.alt || '';
                clonedImg.draggable = false;
            }
        }



        // Calculate scale based on canvas to card ratio
        const canvas = document.querySelector('#posterCanvas') || this.posterMaker?.canvas;
        const canvasWidth = canvas?.offsetWidth || 800;
        const canvasHeight = canvas?.offsetHeight || 600;

        // Get actual card content dimensions
        let cardWidth = 240; // Default trading card width
        let cardHeight = 336; // Default trading card height

        // Use provided cardContent or find existing card
        const targetCardContent = cardContent || document.querySelector('.kms-trading-card-content');
        if (targetCardContent) {
            cardWidth = targetCardContent.offsetWidth || cardWidth;
            cardHeight = targetCardContent.offsetHeight || cardHeight;
        } else {
            // Fallback to layout-based dimensions
            if (this.currentLayout === 'letter') {
                cardWidth = 240;
                cardHeight = 336;
            } else if (this.currentLayout === '4x6') {
                cardWidth = 336; // Landscape orientation
                cardHeight = 240;
            }
        }



        // Copy all computed styles from original element
        this.copyElementStyles(element, clone);

        // Remove old resize handles
        const oldHandles = clone.querySelectorAll('.kms-resize-handle');
        oldHandles.forEach(handle => handle.remove());

        // Scale and position based on element type
        if (element.classList.contains('kms-text-element')) {
            clone.classList.add('kms-trading-card-text-element');
            this.scaleTextElement(clone, originalData, canvasWidth, canvasHeight, cardWidth, cardHeight);
            // Add resize handles for text elements in trading cards
            this.addResizeHandlesToElement(clone);
        } else if (element.classList.contains('kms-image-element')) {
            clone.classList.add('kms-trading-card-image-element');
            this.scaleImageElement(clone, originalData, canvasWidth, canvasHeight, cardWidth, cardHeight);
            // Add resize handles for image elements in trading cards
            this.addResizeHandlesToElement(clone);
        } else if (element.classList.contains('kms-qr-element')) {
            clone.classList.add('kms-trading-card-qr-element');
            this.scaleQRElement(clone, originalData, canvasWidth, canvasHeight, cardWidth, cardHeight);
            // Add resize handles for QR elements in trading cards
            this.addResizeHandlesToElement(clone);
        }

        // Remove other interactive elements but keep resize handles
        const controls = clone.querySelectorAll('.kms-element-controls');
        controls.forEach(control => control.remove());

        // Setup interaction for trading card elements
        this.setupTradingCardElementInteraction(clone);

        return clone;
    }
    
    // Add resize handles to trading card elements
    addResizeHandlesToElement(element) {
        // Remove existing handles first
        const existingHandles = element.querySelectorAll('.kms-resize-handle');
        existingHandles.forEach(handle => handle.remove());
        
        const handles = ['nw', 'ne', 'sw', 'se', 'n', 's', 'w', 'e'];
        
        handles.forEach(direction => {
            const handle = document.createElement('div');
            handle.className = `kms-resize-handle ${direction}`;
            handle.addEventListener('mousedown', (e) => {
                e.stopPropagation();
                this.startElementResize(e, element, direction);
            });
            element.appendChild(handle);
        });
    }
    
    // Setup interaction for trading card elements
    setupTradingCardElementInteraction(element) {
        // Make element selectable and draggable within trading card
        element.addEventListener('mousedown', (e) => {
            if (!e.target.classList.contains('kms-resize-handle')) {
                this.selectTradingCardElement(element);
            }
        });
        
        element.addEventListener('click', (e) => {
            e.stopPropagation();
            this.selectTradingCardElement(element);
        });
    }
    
    // Select trading card element
    selectTradingCardElement(element) {
        // Remove selection from other elements
        const allElements = document.querySelectorAll('.kms-trading-card-element');
        allElements.forEach(el => el.classList.remove('selected'));
        
        // Select current element
        element.classList.add('selected');
        
        // Update poster maker's selected element
        if (this.posterMaker) {
            this.posterMaker.selectedElement = element;
            this.posterMaker.updateControlsForElement(element);
        }
    }
    
    // Start resize for trading card elements
    startElementResize(e, element, direction) {
        e.preventDefault();
        
        const startX = e.clientX;
        const startY = e.clientY;
        const startWidth = parseInt(window.getComputedStyle(element).width);
        const startHeight = parseInt(window.getComputedStyle(element).height);
        const startLeft = parseInt(element.style.left);
        const startTop = parseInt(element.style.top);
        
        const handleResize = (e) => {
            const deltaX = e.clientX - startX;
            const deltaY = e.clientY - startY;
            
            let newWidth = startWidth;
            let newHeight = startHeight;
            let newLeft = startLeft;
            let newTop = startTop;
            
            // Calculate new dimensions based on direction
            switch (direction) {
                case 'se':
                    newWidth = Math.max(20, startWidth + deltaX);
                    newHeight = Math.max(20, startHeight + deltaY);
                    break;
                case 'sw':
                    newWidth = Math.max(20, startWidth - deltaX);
                    newHeight = Math.max(20, startHeight + deltaY);
                    newLeft = startLeft + deltaX;
                    break;
                case 'ne':
                    newWidth = Math.max(20, startWidth + deltaX);
                    newHeight = Math.max(20, startHeight - deltaY);
                    newTop = startTop + deltaY;
                    break;
                case 'nw':
                    newWidth = Math.max(20, startWidth - deltaX);
                    newHeight = Math.max(20, startHeight - deltaY);
                    newLeft = startLeft + deltaX;
                    newTop = startTop + deltaY;
                    break;
                case 'e':
                    newWidth = Math.max(20, startWidth + deltaX);
                    break;
                case 'w':
                    newWidth = Math.max(20, startWidth - deltaX);
                    newLeft = startLeft + deltaX;
                    break;
                case 's':
                    newHeight = Math.max(20, startHeight + deltaY);
                    break;
                case 'n':
                    newHeight = Math.max(20, startHeight - deltaY);
                    newTop = startTop + deltaY;
                    break;
            }
            
            // Apply new dimensions
            element.style.width = newWidth + 'px';
            element.style.height = newHeight + 'px';
            element.style.left = newLeft + 'px';
            element.style.top = newTop + 'px';
            
            // Update image size if it's an image element
            const img = element.querySelector('img');
            if (img) {
                img.style.width = '100%';
                img.style.height = '100%';
                img.style.objectFit = 'contain';
            }
        };
        
        const stopResize = () => {
            document.removeEventListener('mousemove', handleResize);
            document.removeEventListener('mouseup', stopResize);
        };
        
        document.addEventListener('mousemove', handleResize);
        document.addEventListener('mouseup', stopResize);
    }
    
    getElementOriginalData(element) {
        // Get canvas element safely
        const canvas = document.querySelector('#posterCanvas') || this.posterMaker?.canvas;
        if (!canvas) {
            console.warn('Canvas not found, using default positioning');
            return {
                width: element.offsetWidth,
                height: element.offsetHeight,
                fontSize: element.style.fontSize || window.getComputedStyle(element).fontSize,
                transform: element.style.transform,
                position: {
                    left: 0,
                    top: 0,
                    leftPercent: 0,
                    topPercent: 0
                }
            };
        }

        // Use offsetLeft/offsetTop for more accurate positioning relative to canvas
        const relativeLeft = element.offsetLeft;
        const relativeTop = element.offsetTop;
        const canvasWidth = canvas.offsetWidth;
        const canvasHeight = canvas.offsetHeight;

        // Get computed styles for accurate measurements
        const computedStyle = window.getComputedStyle(element);

        const data = {
            width: element.offsetWidth,
            height: element.offsetHeight,
            fontSize: computedStyle.fontSize,
            fontFamily: computedStyle.fontFamily,
            fontWeight: computedStyle.fontWeight,
            color: computedStyle.color,
            backgroundColor: computedStyle.backgroundColor,
            textAlign: computedStyle.textAlign,
            transform: element.style.transform,
            position: {
                left: relativeLeft,
                top: relativeTop,
                leftPercent: (relativeLeft / canvasWidth) * 100,
                topPercent: (relativeTop / canvasHeight) * 100
            }
        };

        // For images, get actual image dimensions and properties
        if (element.classList.contains('kms-image-element')) {
            const img = element.querySelector('img');
            if (img) {
                data.imageWidth = img.offsetWidth;
                data.imageHeight = img.offsetHeight;
                data.imageSrc = img.src;
                data.imageObjectFit = window.getComputedStyle(img).objectFit;
            }
        }

        // For QR codes, get QR dimensions and data
        if (element.classList.contains('kms-qr-element')) {
            const qrImg = element.querySelector('img');
            if (qrImg) {
                data.qrWidth = qrImg.offsetWidth;
                data.qrHeight = qrImg.offsetHeight;
                data.qrSrc = qrImg.src;
            }
            // Get QR data from dataset
            data.qrUrl = element.dataset.qrUrl;
            data.qrSize = element.dataset.qrSize;
            data.qrForeground = element.dataset.qrForeground;
            data.qrBackground = element.dataset.qrBackground;
        }

        return data;
    }

    copyElementStyles(originalElement, clonedElement) {
        // Copy important computed styles
        const computedStyle = window.getComputedStyle(originalElement);
        const stylesToCopy = [
            'fontSize', 'fontFamily', 'fontWeight', 'fontStyle',
            'color', 'backgroundColor', 'textAlign', 'textDecoration',
            'lineHeight', 'letterSpacing', 'wordSpacing',
            'border', 'borderRadius', 'padding', 'margin',
            'opacity', 'textShadow', 'boxShadow'
        ];

        stylesToCopy.forEach(property => {
            const value = computedStyle.getPropertyValue(property);
            if (value && value !== 'initial' && value !== 'normal') {
                clonedElement.style[property] = value;
            }
        });

        // Copy any inline styles that might be important
        if (originalElement.style.cssText) {
            const inlineStyles = originalElement.style.cssText.split(';');
            inlineStyles.forEach(style => {
                if (style.trim()) {
                    const [property, value] = style.split(':');
                    if (property && value) {
                        clonedElement.style.setProperty(property.trim(), value.trim());
                    }
                }
            });
        }
    }

    scaleTextElement(element, originalData = null, canvasWidth, canvasHeight, cardWidth, cardHeight) {
        let currentFontSize = 16;

        if (originalData && originalData.fontSize) {
            currentFontSize = parseInt(originalData.fontSize) || 16;
        } else {
            const computedStyle = window.getComputedStyle(element);
            currentFontSize = parseInt(computedStyle.fontSize) || 16;
        }

        // Calculate proper scale based on canvas to card ratio
        const scaleX = cardWidth / canvasWidth;
        const scaleY = cardHeight / canvasHeight;
        const properScale = Math.min(scaleX, scaleY);

        // Scale font size but ensure it's readable
        const newFontSize = Math.max(8, Math.min(24, currentFontSize * properScale));
        element.style.fontSize = newFontSize + 'px';



        // Calculate position based on percentage
        if (originalData && originalData.position) {
            const leftPercent = originalData.position.leftPercent || 0;
            const topPercent = originalData.position.topPercent || 0;

            const newLeft = (leftPercent / 100) * cardWidth;
            const newTop = (topPercent / 100) * cardHeight;

            element.style.position = 'absolute';
            element.style.left = Math.max(0, newLeft) + 'px';
            element.style.top = Math.max(0, newTop) + 'px';
            element.style.width = 'auto';
            element.style.height = 'auto';


        } else {
            element.style.position = 'relative';
            element.style.margin = '2px auto';
            element.style.display = 'block';
        }

        // Ensure text fits within card
        element.style.maxWidth = '95%';
        element.style.wordWrap = 'break-word';
        element.style.overflow = 'hidden';
        element.style.boxSizing = 'border-box';

        // Remove any transform that might interfere
        element.style.transform = 'none';
    }
    
    scaleImageElement(element, originalData = null, canvasWidth, canvasHeight, cardWidth, cardHeight) {
        const img = element.querySelector('img');
        if (img) {
            let originalWidth = 100;
            let originalHeight = 100;

            if (originalData) {
                originalWidth = originalData.imageWidth || originalData.width || 100;
                originalHeight = originalData.imageHeight || originalData.height || 100;
            } else {
                originalWidth = img.offsetWidth || 100;
                originalHeight = img.offsetHeight || 100;
            }

            // Calculate proper scale based on canvas to card ratio
            const scaleX = cardWidth / canvasWidth;
            const scaleY = cardHeight / canvasHeight;
            const properScale = Math.min(scaleX, scaleY);

            // Scale dimensions but ensure they fit within card
            const scaledWidth = Math.min(originalWidth * properScale, cardWidth * 0.8);
            const scaledHeight = Math.min(originalHeight * properScale, cardHeight * 0.8);

            // Maintain aspect ratio
            const aspectRatio = originalWidth / originalHeight;
            let finalWidth = scaledWidth;
            let finalHeight = scaledHeight;

            if (scaledWidth / scaledHeight > aspectRatio) {
                finalWidth = scaledHeight * aspectRatio;
            } else {
                finalHeight = scaledWidth / aspectRatio;
            }

            // Set the scaled dimensions
            img.style.width = finalWidth + 'px';
            img.style.height = finalHeight + 'px';
            img.style.objectFit = 'contain';

            // Update element dimensions to match image
            element.style.width = finalWidth + 'px';
            element.style.height = finalHeight + 'px';


        }

        // Calculate position based on percentage
        if (originalData && originalData.position) {
            const leftPercent = originalData.position.leftPercent || 0;
            const topPercent = originalData.position.topPercent || 0;

            const newLeft = (leftPercent / 100) * cardWidth;
            const newTop = (topPercent / 100) * cardHeight;

            element.style.position = 'absolute';
            element.style.left = Math.max(0, newLeft) + 'px';
            element.style.top = Math.max(0, newTop) + 'px';


        } else {
            element.style.position = 'relative';
            element.style.margin = '2px auto';
            element.style.display = 'block';
        }

        element.style.transform = 'none';
        element.style.textAlign = 'center';
        element.style.overflow = 'hidden';
    }
    
    scaleQRElement(element, originalData = null, canvasWidth, canvasHeight, cardWidth, cardHeight) {
        const qrImg = element.querySelector('img');
        if (qrImg) {
            let originalSize = 100;

            if (originalData) {
                originalSize = originalData.qrWidth || originalData.qrHeight || 100;
            } else {
                originalSize = qrImg.offsetWidth || 100;
            }

            // Calculate proper scale based on canvas to card ratio
            const scaleX = cardWidth / canvasWidth;
            const scaleY = cardHeight / canvasHeight;
            const properScale = Math.min(scaleX, scaleY);

            const scaledSize = originalSize * properScale;

            // Set minimum and maximum readable size for QR codes
            const minQRSize = 30;
            const maxQRSize = Math.min(cardWidth * 0.4, cardHeight * 0.4);
            const finalSize = Math.max(minQRSize, Math.min(maxQRSize, scaledSize));

            qrImg.style.width = finalSize + 'px';
            qrImg.style.height = finalSize + 'px';

            // Update element dimensions to match QR image
            element.style.width = finalSize + 'px';
            element.style.height = finalSize + 'px';


        }

        // Calculate position based on percentage
        if (originalData && originalData.position) {
            const leftPercent = originalData.position.leftPercent || 0;
            const topPercent = originalData.position.topPercent || 0;

            const newLeft = (leftPercent / 100) * cardWidth;
            const newTop = (topPercent / 100) * cardHeight;

            element.style.position = 'absolute';
            element.style.left = Math.max(0, newLeft) + 'px';
            element.style.top = Math.max(0, newTop) + 'px';


        } else {
            element.style.position = 'relative';
            element.style.margin = '2px auto';
            element.style.display = 'block';
        }

        element.style.transform = 'none';
        element.style.textAlign = 'center';
        element.style.overflow = 'hidden';
    }
    
    // Method to apply specific content type to all cards
    applyTextToAllCards(text, styles = {}) {
        this.tradingCards.forEach(card => {
            const cardContent = card.querySelector('.kms-trading-card-content');
            if (cardContent) {
                let textElement = cardContent.querySelector('.kms-trading-card-text-element');
                if (!textElement) {
                    textElement = document.createElement('div');
                    textElement.className = 'kms-trading-card-text-element';
                    cardContent.appendChild(textElement);
                }
                
                textElement.textContent = text;
                
                // Apply styles
                Object.keys(styles).forEach(property => {
                    textElement.style[property] = styles[property];
                });
            }
        });
    }
    
    applyImageToAllCards(imageSrc) {
        this.tradingCards.forEach(card => {
            const cardContent = card.querySelector('.kms-trading-card-content');
            if (cardContent) {
                let imageElement = cardContent.querySelector('.kms-trading-card-image-element');
                if (!imageElement) {
                    imageElement = document.createElement('div');
                    imageElement.className = 'kms-trading-card-image-element';
                    const img = document.createElement('img');
                    imageElement.appendChild(img);
                    cardContent.appendChild(imageElement);
                }
                
                const img = imageElement.querySelector('img');
                if (img) {
                    img.src = imageSrc;
                    img.style.maxWidth = '60px';
                    img.style.maxHeight = '60px';
                    img.style.objectFit = 'contain';
                }
            }
        });
    }
    
    applyQRToAllCards(qrData) {
        this.tradingCards.forEach(card => {
            const cardContent = card.querySelector('.kms-trading-card-content');
            if (cardContent) {
                let qrElement = cardContent.querySelector('.kms-trading-card-qr-element');
                if (!qrElement) {
                    qrElement = document.createElement('div');
                    qrElement.className = 'kms-trading-card-qr-element';
                    cardContent.appendChild(qrElement);
                }
                
                // Generate QR code
                if (window.qrcode) {
                    const qr = qrcode(0, 'M');
                    qr.addData(qrData);
                    qr.make();
                    qrElement.innerHTML = qr.createImgTag(2, 0);
                    
                    const img = qrElement.querySelector('img');
                    if (img) {
                        img.style.width = '50px';
                        img.style.height = '50px';
                    }
                }
            }
        });
    }
    
    clearTradingCards() {
        this.tradingCards.forEach(card => {
            if (card.parentNode) {
                card.parentNode.removeChild(card);
            }
        });
        this.tradingCards = [];
        this.selectedCard = null;
    }
    
    clearCanvas() {
        const canvas = document.getElementById('posterCanvas');
        if (!canvas) return;
        
        // 清除所有子元素
        while (canvas.firstChild) {
            canvas.removeChild(canvas.firstChild);
        }
    }
    
    updateLayout() {
        if (this.tradingCards.length > 0) {
            this.generateTradingCards();
        }
    }
    
    showMessage(message) {
        // 創建臨時消息提示
        const messageDiv = document.createElement('div');
        messageDiv.style.cssText = `
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: var(--color-1);
            color: var(--text-color-2);
            padding: 16px 24px;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.3);
            z-index: 10000;
            font-weight: bold;
        `;
        messageDiv.textContent = message;
        
        document.body.appendChild(messageDiv);
        
        setTimeout(() => {
            if (messageDiv.parentNode) {
                messageDiv.parentNode.removeChild(messageDiv);
            }
        }, 2000);
    }
    
    // 獲取當前交易卡數據（用於保存/載入）
    getTradingCardData() {
        return {
            layout: this.currentLayout,
            cards: this.tradingCards.map(card => ({
                cardNumber: card.dataset.cardNumber,
                content: card.querySelector('.kms-trading-card-content').innerHTML,
                styles: card.querySelector('.kms-trading-card-content').style.cssText
            }))
        };
    }
    
    // 載入交易卡數據
    loadTradingCardData(data) {
        if (!data || !data.layout) return;
        
        // 設置紙張類型
        const paperSelect = document.getElementById('tradingCardPaper');
        if (paperSelect) {
            paperSelect.value = data.layout;
        }
        
        // 生成佈局
        this.generateTradingCards();
        
        // 恢復內容
        if (data.cards) {
            data.cards.forEach(cardData => {
                const card = this.tradingCards.find(c => 
                    c.dataset.cardNumber === cardData.cardNumber);
                if (card) {
                    const contentArea = card.querySelector('.kms-trading-card-content');
                    if (contentArea) {
                        contentArea.innerHTML = cardData.content;
                        contentArea.style.cssText = cardData.styles;
                    }
                }
            });
        }
    }
}

// 導出類
window.KMSTradingCard = KMSTradingCard;