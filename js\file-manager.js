/**
 * <PERSON><PERSON> Poster Maker - File Manager
 * Handles poster file management including save, load, and manage operations
 */

class KMSFileManager {
    constructor(posterMaker) {
        this.posterMaker = posterMaker;
        this.isModalOpen = false;
        this.initializeEventListeners();
    }

    initializeEventListeners() {
        // Save poster button
        const saveBtn = document.getElementById('savePosterBtn');
        if (saveBtn) {
            saveBtn.addEventListener('click', () => this.showSaveDialog());
        }

        // Load poster button
        const loadBtn = document.getElementById('loadPosterBtn');
        if (loadBtn) {
            loadBtn.addEventListener('click', () => this.showLoadDialog());
        }

        // Manage saved posters button
        const manageBtn = document.getElementById('manageSavedBtn');
        if (manageBtn) {
            manageBtn.addEventListener('click', () => this.showManageDialog());
        }

        // Close modal when clicking outside
        document.addEventListener('click', (e) => {
            if (e.target.classList.contains('kms-modal-overlay')) {
                this.closeModal();
            }
        });

        // ESC key to close modal
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape' && this.isModalOpen) {
                this.closeModal();
            }
        });
    }

    showSaveDialog() {
        const t = this.posterMaker.translations[this.posterMaker.currentLanguage];
        const modal = this.createModal({
            title: t.savePoster,
            content: `
                <div class="kms-save-dialog">
                    <div class="kms-form-group">
                        <label for="posterNameInput">${t.posterName}:</label>
                        <input type="text" id="posterNameInput" class="kms-input" 
                               placeholder="${t.enterPosterName}" 
                               value="Poster_${new Date().toLocaleDateString().replace(/\//g, '-')}">
                    </div>
                    <div class="kms-form-actions">
                        <button class="kms-btn kms-btn-secondary" onclick="window.kmsFileManager.closeModal()">
                            ${t.cancel}
                        </button>
                        <button class="kms-btn kms-btn-primary" onclick="window.kmsFileManager.savePoster()">
                            ${t.save}
                        </button>
                        <button class="kms-btn kms-btn-info" onclick="window.kmsFileManager.exportPoster()">
                            ${t.exportJSON}
                        </button>
                    </div>
                </div>
            `
        });

        // Focus on input
        setTimeout(() => {
            const input = document.getElementById('posterNameInput');
            if (input) {
                input.focus();
                input.select();
            }
        }, 100);

        // Enter key to save
        const input = document.getElementById('posterNameInput');
        if (input) {
            input.addEventListener('keydown', (e) => {
                if (e.key === 'Enter') {
                    this.savePoster();
                }
            });
        }
    }

    async showLoadDialog() {
        const t = this.posterMaker.translations[this.posterMaker.currentLanguage];
        
        // 更新快取並取得已儲存的海報
        await this.posterMaker.posterData.updateCachedPosters();
        const savedPosters = this.posterMaker.posterData.getSavedPostersSync();

        if (savedPosters.length === 0) {
            this.showNoPostersMessage();
            return;
        }

        // Initialize pagination variables
        this.currentPage = 1;
        this.postersPerPage = 12;
        this.filteredPosters = savedPosters;

        const modal = this.createModal({
            title: `${t.loadPoster} (${savedPosters.length} ${t.total || 'total'})`,
            content: `
                <div class="kms-load-dialog">
                    <div class="kms-import-section">
                        <label for="importFileInput" class="kms-import-label">
                            ${t.importFromFile}:
                        </label>
                        <input type="file" id="importFileInput" accept=".json" class="kms-file-input">
                        <button class="kms-btn kms-btn-info" onclick="window.kmsFileManager.importFromFile()">
                            ${t.import}
                        </button>
                    </div>
                    <div class="kms-search-section">
                        <input type="text" id="posterSearchInput" class="kms-input" 
                               placeholder="${t.searchPosters || 'Search posters...'}" 
                               oninput="window.kmsFileManager.filterPosters()">
                        <div class="kms-search-info">
                            <span id="posterCount">${savedPosters.length}</span> ${t.postersFound || 'posters found'}
                        </div>
                    </div>
                    <div class="kms-posters-list" id="postersListContainer">
                        <!-- Posters will be loaded here -->
                    </div>
                    <div class="kms-pagination" id="paginationContainer">
                        <!-- Pagination will be loaded here -->
                    </div>
                    <div class="kms-form-actions">
                        <button class="kms-btn kms-btn-secondary" onclick="window.kmsFileManager.closeModal()">
                            ${t.cancel}
                        </button>
                    </div>
                </div>
            `,
            size: 'large'
        });

        // Load first page
        this.loadPostersPage();
    }

    async showManageDialog() {
        const t = this.posterMaker.translations[this.posterMaker.currentLanguage];
        
        // 更新快取並取得已儲存的海報
        await this.posterMaker.posterData.updateCachedPosters();
        const savedPosters = this.posterMaker.posterData.getSavedPostersSync();

        if (savedPosters.length === 0) {
            this.showNoPostersMessage();
            return;
        }

        const postersList = savedPosters.map(poster => {
            const date = new Date(poster.timestamp).toLocaleDateString();
            const time = new Date(poster.timestamp).toLocaleTimeString();
            const thumbnailData = poster.thumbnail || '';
            return `
                <div class="kms-poster-item" data-poster-id="${poster.id}">
                    <div class="kms-poster-thumbnail">
                        ${thumbnailData ? 
                            `<img src="${thumbnailData}" alt="${poster.name}" class="kms-thumbnail-img">` :
                            `<div class="kms-thumbnail-placeholder">
                                <i class="fas fa-image"></i>
                                <span>${t.noPreview}</span>
                            </div>`
                        }
                    </div>
                    <div class="kms-poster-info">
                        <h4 class="kms-poster-name">${poster.name}</h4>
                        <p class="kms-poster-details">
                            ${t.paper}: ${poster.paperSize} | 
                            ${t.elements}: ${poster.elements ? poster.elements.length : 0} | 
                            ${date} ${time}
                        </p>
                    </div>
                    <div class="kms-poster-actions">
                        <button class="kms-btn kms-btn-small kms-btn-primary" 
                                onclick="window.kmsFileManager.loadPoster('${poster.id}')">
                            ${t.load}
                        </button>
                        <button class="kms-btn kms-btn-small kms-btn-info" 
                                onclick="window.kmsFileManager.exportPosterById('${poster.id}')">
                            ${t.export}
                        </button>
                        <button class="kms-btn kms-btn-small kms-btn-danger" 
                                onclick="window.kmsFileManager.deletePoster('${poster.id}')">
                            ${t.delete}
                        </button>
                    </div>
                </div>
            `;
        }).join('');

        const modal = this.createModal({
            title: t.manageSaved,
            content: `
                <div class="kms-manage-dialog">
                    <div class="kms-manage-header">
                        <p class="kms-manage-info">
                            ${t.total}: ${savedPosters.length} ${t.posters}
                        </p>
                        <div class="kms-manage-actions">
                            <button class="kms-btn kms-btn-info" onclick="window.kmsFileManager.showStorageManager()">
                                ${t.storageManager || 'Storage Manager'}
                            </button>
                            <button class="kms-btn kms-btn-warning" onclick="window.kmsFileManager.clearAllPosters()">
                                ${t.clearAll}
                            </button>
                        </div>
                    </div>
                    <div class="kms-posters-list">
                        ${postersList}
                    </div>
                    <div class="kms-form-actions">
                        <button class="kms-btn kms-btn-secondary" onclick="window.kmsFileManager.closeModal()">
                            ${t.close}
                        </button>
                    </div>
                </div>
            `,
            size: 'large'
        });
    }

    showNoPostersMessage() {
        const t = this.posterMaker.translations[this.posterMaker.currentLanguage];
        const modal = this.createModal({
            title: t.noSavedPosters,
            content: `
                <div class="kms-no-posters">
                    <p>${t.noSavedPostersMessage}</p>
                    <p>${t.createPosterMessage}</p>
                    <div class="kms-form-actions">
                        <button class="kms-btn kms-btn-primary" onclick="window.kmsFileManager.closeModal()">
                            ${t.ok}
                        </button>
                    </div>
                </div>
            `
        });
    }

    createModal({ title, content, size = 'medium' }) {
        // Remove existing modal if any
        this.closeModal();

        const modal = document.createElement('div');
        modal.className = 'kms-modal-overlay';
        modal.innerHTML = `
            <div class="kms-modal kms-modal-${size}">
                <div class="kms-modal-header">
                    <h3 class="kms-modal-title">${title}</h3>
                    <button class="kms-modal-close" onclick="window.kmsFileManager.closeModal()">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <div class="kms-modal-content">
                    ${content}
                </div>
            </div>
        `;

        document.body.appendChild(modal);
        this.isModalOpen = true;
        
        // Add animation
        setTimeout(() => {
            modal.classList.add('kms-modal-show');
        }, 10);

        return modal;
    }

    closeModal() {
        const modal = document.querySelector('.kms-modal-overlay');
        if (modal) {
            modal.classList.remove('kms-modal-show');
            setTimeout(() => {
                if (modal.parentNode) {
                    modal.parentNode.removeChild(modal);
                }
            }, 300);
        }
        this.isModalOpen = false;
    }

    savePoster() {
        const nameInput = document.getElementById('posterNameInput');
        const posterName = nameInput ? nameInput.value.trim() : '';
        
        if (!posterName) {
            const t = this.posterMaker.translations[this.posterMaker.currentLanguage];
            alert(t.enterPosterName);
            return;
        }

        // Generate thumbnail before saving
        this.generateThumbnail().then(thumbnailData => {
            const posterData = this.posterMaker.posterData.savePosterData(posterName);
            
            // Add thumbnail to saved data
                if (thumbnailData) {
                    const savedPosters = this.posterMaker.posterData.getSavedPostersSync();
                    const savedPoster = savedPosters.find(p => p.id === posterData.id);
                    if (savedPoster) {
                        savedPoster.thumbnail = thumbnailData;
                        localStorage.setItem('kms_saved_posters', JSON.stringify(savedPosters));
                    }
                }
            
            this.closeModal();
        }).catch(error => {
            console.error('Error generating thumbnail:', error);
            // Save without thumbnail if generation fails
            this.posterMaker.posterData.savePosterData(posterName);
            this.closeModal();
        });
    }

    exportPoster() {
        const nameInput = document.getElementById('posterNameInput');
        const posterName = nameInput ? nameInput.value.trim() : '';
        
        if (!posterName) {
            const t = this.posterMaker.translations[this.posterMaker.currentLanguage];
            alert(t.enterPosterName);
            return;
        }

        // Temporarily save with the name and export
        const posterData = this.posterMaker.posterData.savePosterData(posterName);
        const dataStr = JSON.stringify(posterData, null, 2);
        const dataBlob = new Blob([dataStr], { type: 'application/json' });
        
        const link = document.createElement('a');
        link.href = URL.createObjectURL(dataBlob);
        link.download = `${posterName}.json`;
        link.click();
        
        this.closeModal();
    }

    loadPoster(posterId) {
        const savedPosters = this.posterMaker.posterData.getSavedPostersSync();
        const poster = savedPosters.find(p => p.id === posterId);
        
        if (poster) {
            this.posterMaker.posterData.loadPosterData(poster);
            this.closeModal();
        }
    }

    /**
     * 匯出指定的海報為 JSON 檔案
     */
    exportPosterById(posterId) {
        const savedPosters = this.posterMaker.posterData.getSavedPostersSync();
        const poster = savedPosters.find(p => p.id === posterId);
        
        if (poster) {
            const dataStr = JSON.stringify(poster, null, 2);
            const dataBlob = new Blob([dataStr], {type: 'application/json'});
            
            const link = document.createElement('a');
            link.href = URL.createObjectURL(dataBlob);
            link.download = `${poster.name}.json`;
            link.click();
        } else {
            const t = this.posterMaker.translations[this.posterMaker.currentLanguage];
            alert(t.posterNotFound || 'Poster not found!');
        }
    }

    async deletePoster(posterId) {
        const t = this.posterMaker.translations[this.posterMaker.currentLanguage];
        
        if (confirm(t.confirmDelete)) {
            const success = await this.posterMaker.posterData.deleteSavedPoster(posterId);
            if (success) {
                // Refresh the manage dialog
                this.showManageDialog();
            }
        }
    }

    clearAllPosters() {
        const t = this.posterMaker.translations[this.posterMaker.currentLanguage];
        
        if (confirm(t.confirmDeleteAll)) {
            localStorage.removeItem('kms_saved_posters');
            this.closeModal();
            
            this.posterMaker.posterData.showToast(t.posterDeleted, 'info');
        }
    }

    importFromFile() {
        const fileInput = document.getElementById('importFileInput');
        const file = fileInput ? fileInput.files[0] : null;
        
        if (!file) {
            const t = this.posterMaker.translations[this.posterMaker.currentLanguage];
            alert(t.selectJSONFile);
            return;
        }

        this.posterMaker.posterData.importPosterFromJSON(file);
        this.closeModal();
    }

    // Generate thumbnail of current poster
    generateThumbnail() {
        return new Promise((resolve, reject) => {
            try {
                const canvas = document.getElementById('posterCanvas');
                if (!canvas) {
                    resolve(null);
                    return;
                }

                // Create a much smaller thumbnail to save space
                const thumbnailCanvas = document.createElement('canvas');
                const ctx = thumbnailCanvas.getContext('2d');
                
                // Set very small thumbnail size to minimize data
                const maxWidth = 80;  // Reduced from 200
                const maxHeight = 60; // Reduced from 150
                const canvasRect = canvas.getBoundingClientRect();
                const aspectRatio = canvasRect.width / canvasRect.height;
                
                let thumbnailWidth, thumbnailHeight;
                if (aspectRatio > maxWidth / maxHeight) {
                    thumbnailWidth = maxWidth;
                    thumbnailHeight = maxWidth / aspectRatio;
                } else {
                    thumbnailHeight = maxHeight;
                    thumbnailWidth = maxHeight * aspectRatio;
                }
                
                thumbnailCanvas.width = thumbnailWidth;
                thumbnailCanvas.height = thumbnailHeight;
                
                // Fill background
                ctx.fillStyle = canvas.style.backgroundColor || '#ffffff';
                ctx.fillRect(0, 0, thumbnailWidth, thumbnailHeight);
                
                // Scale factor
                const scaleX = thumbnailWidth / canvasRect.width;
                const scaleY = thumbnailHeight / canvasRect.height;
                
                // Draw elements
                const elements = this.posterMaker.elements;
                const promises = elements.map(element => {
                    return this.drawElementOnThumbnail(ctx, element, scaleX, scaleY);
                });
                
                Promise.all(promises).then(() => {
                    // Convert to data URL with much lower quality to save space
                    const thumbnailData = thumbnailCanvas.toDataURL('image/jpeg', 0.3); // Reduced quality from 0.8 to 0.3
                    resolve(thumbnailData);
                }).catch(reject);
                
            } catch (error) {
                console.error('Error generating thumbnail:', error);
                resolve(null);
            }
        });
    }
    
    drawElementOnThumbnail(ctx, element, scaleX, scaleY) {
        return new Promise((resolve) => {
            try {
                const rect = element.getBoundingClientRect();
                const canvasRect = document.getElementById('posterCanvas').getBoundingClientRect();
                
                const x = (rect.left - canvasRect.left) * scaleX;
                const y = (rect.top - canvasRect.top) * scaleY;
                const width = rect.width * scaleX;
                const height = rect.height * scaleY;
                
                if (element.classList.contains('kms-text-element')) {
                    // Draw text element
                    const computedStyle = window.getComputedStyle(element);
                    ctx.fillStyle = computedStyle.color || '#000000';
                    ctx.font = `${Math.max(8, parseInt(computedStyle.fontSize) * scaleX)}px ${computedStyle.fontFamily || 'Arial'}`;
                    ctx.textAlign = computedStyle.textAlign || 'left';
                    
                    // Draw background if exists
                    if (computedStyle.backgroundColor && computedStyle.backgroundColor !== 'rgba(0, 0, 0, 0)') {
                        ctx.fillStyle = computedStyle.backgroundColor;
                        ctx.fillRect(x, y, width, height);
                        ctx.fillStyle = computedStyle.color || '#000000';
                    }
                    
                    // Draw text (simplified)
                    const text = element.textContent || element.innerText || '';
                    const lines = text.split('\n');
                    const lineHeight = Math.max(10, parseInt(computedStyle.fontSize) * scaleX * 1.2);
                    
                    lines.forEach((line, index) => {
                        ctx.fillText(line, x + 2, y + lineHeight * (index + 1));
                    });
                    
                } else if (element.classList.contains('kms-image-element')) {
                    // Draw image element
                    const img = element.querySelector('img');
                    if (img && img.complete && img.naturalHeight !== 0) {
                        ctx.drawImage(img, x, y, width, height);
                    } else {
                        // Draw placeholder
                        ctx.fillStyle = '#f0f0f0';
                        ctx.fillRect(x, y, width, height);
                        ctx.strokeStyle = '#ccc';
                        ctx.strokeRect(x, y, width, height);
                        
                        ctx.fillStyle = '#999';
                        ctx.font = `${Math.max(8, 12 * scaleX)}px Arial`;
                        ctx.textAlign = 'center';
                        ctx.fillText('IMG', x + width/2, y + height/2);
                    }
                    
                } else if (element.classList.contains('kms-qr-element')) {
                    // Draw QR code placeholder
                    ctx.fillStyle = '#ffffff';
                    ctx.fillRect(x, y, width, height);
                    ctx.strokeStyle = '#000000';
                    ctx.strokeRect(x, y, width, height);
                    
                    // Draw simple QR pattern
                    const cellSize = Math.max(1, width / 10);
                    ctx.fillStyle = '#000000';
                    for (let i = 0; i < 10; i += 2) {
                        for (let j = 0; j < 10; j += 2) {
                            ctx.fillRect(x + i * cellSize, y + j * cellSize, cellSize, cellSize);
                        }
                    }
                }
                
                resolve();
            } catch (error) {
                console.error('Error drawing element on thumbnail:', error);
                resolve();
            }
        });
    }

    // Load posters page with pagination
    loadPostersPage() {
        const container = document.getElementById('postersListContainer');
        const paginationContainer = document.getElementById('paginationContainer');
        
        if (!container || !this.filteredPosters) return;

        const startIndex = (this.currentPage - 1) * this.postersPerPage;
        const endIndex = startIndex + this.postersPerPage;
        const postersToShow = this.filteredPosters.slice(startIndex, endIndex);
        const t = this.posterMaker.translations[this.posterMaker.currentLanguage];

        // Generate posters HTML
        const postersList = postersToShow.map(poster => {
            const date = new Date(poster.timestamp).toLocaleDateString();
            const time = new Date(poster.timestamp).toLocaleTimeString();
            const thumbnailData = poster.thumbnail || '';
            return `
                <div class="kms-poster-item" data-poster-id="${poster.id}">
                    <div class="kms-poster-thumbnail">
                        ${thumbnailData ? 
                            `<img src="${thumbnailData}" alt="${poster.name}" class="kms-thumbnail-img">` :
                            `<div class="kms-thumbnail-placeholder">
                                <i class="fas fa-image"></i>
                                <span>${t.noPreview}</span>
                            </div>`
                        }
                    </div>
                    <div class="kms-poster-info">
                        <h4 class="kms-poster-name">${poster.name}</h4>
                        <p class="kms-poster-details">
                            ${t.paper}: ${poster.paperSize} | 
                            ${t.elements}: ${poster.elements ? poster.elements.length : 0} | 
                            ${date} ${time}
                        </p>
                    </div>
                    <div class="kms-poster-actions">
                        <button class="kms-btn kms-btn-small kms-btn-primary" 
                                onclick="window.kmsFileManager.loadPoster('${poster.id}')">
                            ${t.load}
                        </button>
                    </div>
                </div>
            `;
        }).join('');

        container.innerHTML = postersList;

        // Generate pagination
        this.generatePagination(paginationContainer);
    }

    // Filter posters based on search input
    filterPosters() {
        const searchInput = document.getElementById('posterSearchInput');
        const posterCount = document.getElementById('posterCount');
        
        if (!searchInput || !posterCount) return;

        const searchTerm = searchInput.value.toLowerCase().trim();
        const allPosters = this.posterMaker.posterData.getSavedPostersSync();

        if (searchTerm === '') {
            this.filteredPosters = allPosters;
        } else {
            this.filteredPosters = allPosters.filter(poster => 
                poster.name.toLowerCase().includes(searchTerm) ||
                poster.paperSize.toLowerCase().includes(searchTerm) ||
                new Date(poster.timestamp).toLocaleDateString().includes(searchTerm)
            );
        }

        // Reset to first page
        this.currentPage = 1;
        
        // Update count
        posterCount.textContent = this.filteredPosters.length;
        
        // Reload page
        this.loadPostersPage();
    }

    // Generate pagination controls
    generatePagination(container) {
        if (!container || !this.filteredPosters) return;

        const totalPages = Math.ceil(this.filteredPosters.length / this.postersPerPage);
        const t = this.posterMaker.translations[this.posterMaker.currentLanguage];

        if (totalPages <= 1) {
            container.innerHTML = '';
            return;
        }

        let paginationHTML = '<div class="kms-pagination-controls">';
        
        // Previous button
        if (this.currentPage > 1) {
            paginationHTML += `
                <button class="kms-btn kms-btn-small kms-btn-secondary" 
                        onclick="window.kmsFileManager.goToPage(${this.currentPage - 1})">
                    ${t.previous || 'Previous'}
                </button>
            `;
        }

        // Page numbers
        const startPage = Math.max(1, this.currentPage - 2);
        const endPage = Math.min(totalPages, this.currentPage + 2);

        if (startPage > 1) {
            paginationHTML += `
                <button class="kms-btn kms-btn-small kms-btn-secondary" 
                        onclick="window.kmsFileManager.goToPage(1)">1</button>
            `;
            if (startPage > 2) {
                paginationHTML += '<span class="kms-pagination-dots">...</span>';
            }
        }

        for (let i = startPage; i <= endPage; i++) {
            const isActive = i === this.currentPage ? 'kms-btn-primary' : 'kms-btn-secondary';
            paginationHTML += `
                <button class="kms-btn kms-btn-small ${isActive}" 
                        onclick="window.kmsFileManager.goToPage(${i})">${i}</button>
            `;
        }

        if (endPage < totalPages) {
            if (endPage < totalPages - 1) {
                paginationHTML += '<span class="kms-pagination-dots">...</span>';
            }
            paginationHTML += `
                <button class="kms-btn kms-btn-small kms-btn-secondary" 
                        onclick="window.kmsFileManager.goToPage(${totalPages})">${totalPages}</button>
            `;
        }

        // Next button
        if (this.currentPage < totalPages) {
            paginationHTML += `
                <button class="kms-btn kms-btn-small kms-btn-secondary" 
                        onclick="window.kmsFileManager.goToPage(${this.currentPage + 1})">
                    ${t.next || 'Next'}
                </button>
            `;
        }

        paginationHTML += '</div>';
        
        // Add page info
        paginationHTML += `
            <div class="kms-pagination-info">
                ${t.page || 'Page'} ${this.currentPage} ${t.of || 'of'} ${totalPages} 
                (${this.filteredPosters.length} ${t.total || 'total'})
            </div>
        `;

        container.innerHTML = paginationHTML;
    }

    // Go to specific page
    goToPage(page) {
        const totalPages = Math.ceil(this.filteredPosters.length / this.postersPerPage);
        
        if (page < 1 || page > totalPages) return;
        
        this.currentPage = page;
        this.loadPostersPage();
    }

    showStorageManager() {
        const t = this.posterMaker.translations[this.posterMaker.currentLanguage];
        const stats = this.posterMaker.posterData.getStorageStatistics();
        
        const modal = this.createModal({
            title: t.storageManager || 'Storage Manager',
            content: `
                <div class="kms-storage-manager">
                    <div class="kms-storage-stats">
                        <h4>${t.storageUsage || 'Storage Usage'}</h4>
                        <div class="kms-storage-info">
                            <div class="kms-storage-item">
                                <span class="kms-storage-label">${t.totalPosters || 'Total Posters'}:</span>
                                <span class="kms-storage-value">${stats.totalPosters}</span>
                            </div>
                            <div class="kms-storage-item">
                                <span class="kms-storage-label">${t.storageUsed || 'Storage Used'}:</span>
                                <span class="kms-storage-value">${stats.usedSizeFormatted}</span>
                            </div>
                            <div class="kms-storage-item">
                                <span class="kms-storage-label">${t.storageLimit || 'Storage Limit'}:</span>
                                <span class="kms-storage-value">${stats.maxSizeFormatted}</span>
                            </div>
                            <div class="kms-storage-item">
                                <span class="kms-storage-label">${t.usagePercentage || 'Usage Percentage'}:</span>
                                <span class="kms-storage-value">${stats.usagePercentage}%</span>
                            </div>
                        </div>
                        <div class="kms-storage-bar">
                            <div class="kms-storage-progress" style="width: ${Math.min(stats.usagePercentage, 100)}%"></div>
                        </div>
                    </div>
                    
                    <div class="kms-storage-actions">
                        <h4>${t.storageActions || 'Storage Actions'}</h4>
                        <div class="kms-storage-buttons">
                            <button class="kms-btn kms-btn-warning" onclick="window.kmsFileManager.performManualCleanup()">
                                ${t.cleanupOldData || 'Cleanup Old Data'}
                            </button>
                            <button class="kms-btn kms-btn-info" onclick="window.kmsFileManager.toggleCompression()">
                                ${stats.compressionEnabled ? (t.disableCompression || 'Disable Compression') : (t.enableCompression || 'Enable Compression')}
                            </button>
                            <button class="kms-btn kms-btn-secondary" onclick="window.kmsFileManager.refreshStorageStats()">
                                ${t.refresh || 'Refresh'}
                            </button>
                        </div>
                    </div>
                    
                    <div class="kms-form-actions">
                        <button class="kms-btn kms-btn-secondary" onclick="window.kmsFileManager.closeModal()">
                            ${t.close}
                        </button>
                    </div>
                </div>
            `,
            size: 'medium'
        });
    }

    performManualCleanup() {
        const t = this.posterMaker.translations[this.posterMaker.currentLanguage];
        
        if (confirm(t.confirmCleanup || 'This will remove 30% of the oldest posters to free up storage space. Continue?')) {
            this.posterMaker.posterData.manualCleanup();
            this.showStorageManager(); // Refresh the storage manager
        }
    }

    toggleCompression() {
        this.posterMaker.posterData.compressionEnabled = !this.posterMaker.posterData.compressionEnabled;
        localStorage.setItem('kms_compression_enabled', this.posterMaker.posterData.compressionEnabled);
        this.showStorageManager(); // Refresh the storage manager
    }

    refreshStorageStats() {
        this.showStorageManager(); // Simply refresh the storage manager
    }
}

// Initialize file manager when main app is ready
document.addEventListener('DOMContentLoaded', () => {
    // Wait for kmsPosterMaker to be fully initialized
    const initFileManager = () => {
        if (window.kmsPosterMaker && window.kmsPosterMaker.posterData) {
            window.kmsFileManager = new KMSFileManager(window.kmsPosterMaker);
        } else {
            setTimeout(initFileManager, 100);
        }
    };
    initFileManager();
});