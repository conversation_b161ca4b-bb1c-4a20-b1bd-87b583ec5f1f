/**
 * K<PERSON> Poster Maker - QR Code Generator
 * 完全按照 KMS_Logo_Photo.app 的實現
 */

class KMSQRGenerator {
    constructor(posterMaker) {
        this.posterMaker = posterMaker;
        this.cache = new Map(); // QR Code 緩存
        this.init();
    }

    init() {
        this.setupQRControls();
        this.initializeRotationButton();
    }
    
    /**
     * 生成 QR Code - 完全按照 KMS_Logo_Photo.app 的實現
     */
    generateQRCode(content, size, foreground = '#000000', background = '#ffffff', border = 4) {
        // 創建緩存鍵
        const cacheKey = `${content}-${size}-${foreground}-${background}-${border}`;

        // 檢查緩存
        if (this.cache.has(cacheKey)) {
            return this.cache.get(cacheKey);
        }

        try {
            // 初始化 QR Code，typeNumber=0 表示自動選擇版本，'L' 表示低錯誤校正
            const typeNumber = 0;
            const errorCorrectionLevel = 'L';
            const qr = qrcode(typeNumber, errorCorrectionLevel);

            // 添加 URL 資料
            qr.addData(content);
            qr.make();

            // 創建畫布
            const canvas = document.createElement('canvas');
            const ctx = canvas.getContext('2d');
            const moduleCount = qr.getModuleCount();

            // 計算每個模組的像素大小，考慮邊框（quiet zone）
            const cellSize = size / (moduleCount + 2 * border);
            canvas.width = size;
            canvas.height = size;

            // 填充背景色
            ctx.fillStyle = background;
            ctx.fillRect(0, 0, size, size);

            // 繪製 QR Code 模組
            ctx.fillStyle = foreground;
            for (let row = 0; row < moduleCount; row++) {
                for (let col = 0; col < moduleCount; col++) {
                    if (qr.isDark(row, col)) {
                        const x = (col + border) * cellSize;
                        const y = (row + border) * cellSize;
                        ctx.fillRect(x, y, cellSize, cellSize);
                    }
                }
            }

            // 緩存結果
            this.cache.set(cacheKey, canvas);

            // 限制緩存大小
            if (this.cache.size > 50) {
                const firstKey = this.cache.keys().next().value;
                this.cache.delete(firstKey);
            }

            return canvas;
        } catch (error) {
            console.error('QR Code 生成失敗:', error);
            return null;
        }
    }

    setupQRControls() {
        // Generate QR button
        const generateBtn = document.getElementById('generateQRBtn');
        const urlInput = document.getElementById('qrUrl');
        const sizeInput = document.getElementById('qrSize');
        const foregroundInput = document.getElementById('qrForeground');
        const backgroundInput = document.getElementById('qrBackground');
        const radiusInput = document.getElementById('qrRadius');
        const borderInput = document.getElementById('qrBorder');

        if (generateBtn && urlInput) {
            generateBtn.addEventListener('click', () => {
                const url = urlInput.value.trim();
                if (this.isValidUrl(url)) {
                    const size = parseInt(sizeInput?.value || 90);
                    const foreground = foregroundInput?.value || '#00ffff';
                    const background = backgroundInput?.value || '#000000';
                    const radius = parseInt(radiusInput?.value || 16);
                    const border = parseInt(borderInput?.value || 1);

                    this.createQRElement(url, size, foreground, background, radius, border);
                } else {
                    alert(this.posterMaker.currentLanguage === 'en'
                        ? 'Please enter a valid URL (e.g., https://example.com)'
                        : '請輸入有效的網址 (例如: https://example.com)');
                }
            });

            // Generate on Enter key
            urlInput.addEventListener('keypress', (e) => {
                if (e.key === 'Enter') {
                    generateBtn.click();
                }
            });

            // Add real-time parameter update for selected QR element
            const inputs = [sizeInput, foregroundInput, backgroundInput, radiusInput, borderInput];
            inputs.forEach(input => {
                if (input) {
                    input.addEventListener('input', () => {
                        this.updateSelectedQRCode();
                    });
                    input.addEventListener('change', () => {
                        this.updateSelectedQRCode();
                    });
                }
            });
        }
    }

    updateSelectedQRCode() {
        const selectedElement = this.posterMaker.selectedElement;
        if (!selectedElement || !selectedElement.classList.contains('kms-qr-element')) {
            return;
        }

        const urlInput = document.getElementById('qrUrl');
        const sizeInput = document.getElementById('qrSize');
        const foregroundInput = document.getElementById('qrForeground');
        const backgroundInput = document.getElementById('qrBackground');
        const radiusInput = document.getElementById('qrRadius');
        const borderInput = document.getElementById('qrBorder');

        const url = urlInput?.value.trim() || selectedElement.dataset.qrUrl;
        if (url && this.isValidUrl(url)) {
            const size = parseInt(sizeInput?.value || 90);
            const foreground = foregroundInput?.value || '#00ffff';
            const background = backgroundInput?.value || '#000000';
            const radius = parseInt(radiusInput?.value || 16);
            const border = parseInt(borderInput?.value || 1);

            this.updateQRElement(selectedElement, url, size, foreground, background, radius, border);
        }
    }
    
    /**
     * 創建 QR Code 元素
     */
    createQRElement(url, size, foreground, background, radius, border) {
        // 生成 QR Code 畫布
        const qrCanvas = this.generateQRCode(url, size, foreground, background, border);
        if (!qrCanvas) return null;

        // 獲取應該添加元素的容器
        const container = window.kmsTradingCard?.getElementContainer() || document.getElementById('posterCanvas');
        if (!container) {
            if (this.posterMaker && this.posterMaker.showMessage) {
                this.posterMaker.showMessage('無法添加 QR Code：請先選擇編輯模式或確保畫布可用');
            }
            return null;
        }

        // 創建 QR Code 容器
        const qrContainer = document.createElement('div');

        // 根據容器類型設置不同的類名
        const isInTradingCard = container.classList.contains('kms-trading-card-content');
        if (isInTradingCard) {
            qrContainer.className = 'kms-trading-card-qr-element';
        } else {
            qrContainer.className = 'kms-canvas-element kms-qr-element';
        }

        qrContainer.dataset.elementId = `qr_${++this.posterMaker.elementCounter}`;
        qrContainer.dataset.qrUrl = url;
        qrContainer.dataset.qrSize = size;
        qrContainer.dataset.qrForeground = foreground;
        qrContainer.dataset.qrBackground = background;
        qrContainer.dataset.qrRadius = radius;
        qrContainer.dataset.qrBorder = border;

        // 創建圖片元素
        const img = document.createElement('img');
        img.className = 'kms-qr-image';
        img.src = qrCanvas.toDataURL('image/png');
        img.draggable = false;

        // 應用圓角效果
        if (radius > 0) {
            img.style.borderRadius = radius + 'px';
            qrContainer.style.borderRadius = radius + 'px';
        }

        qrContainer.appendChild(img);

        // 設置容器樣式
        qrContainer.style.width = size + 'px';
        qrContainer.style.height = size + 'px';

        // 計算居中位置
        const containerRect = container.getBoundingClientRect();
        const centerX = (containerRect.width - size) / 2;
        const centerY = (containerRect.height - size) / 2;

        qrContainer.style.left = Math.max(0, centerX) + 'px';
        qrContainer.style.top = Math.max(0, centerY) + 'px';

        // 設置交互
        this.posterMaker.setupElementInteraction(qrContainer);
        this.setupQRInteractions(qrContainer);
        this.addResizeHandles(qrContainer);

        // 直接添加到容器中
        if (isInTradingCard) {
            container.appendChild(qrContainer);

            // 添加到 elements 數組中以支持 Layer List
            if (!this.posterMaker.elements.includes(qrContainer)) {
                this.posterMaker.elements.push(qrContainer);
            }

            // 更新 Layer Manager
            if (this.posterMaker.layerManager) {
                this.posterMaker.layerManager.onElementAdded(qrContainer);
            }
        } else {
            // 使用主要的 poster maker 方法添加元素（包括圖層管理）
            this.posterMaker.addElementToCanvas(qrContainer);
        }

        // 如果是在 trading card 模式且是同步模式，觸發同步
        if (isInTradingCard && window.kmsTradingCard?.editMode === 'sync') {
            // 延遲觸發同步，讓元素先完全添加
            setTimeout(() => {
                window.kmsTradingCard.syncCard1ToAll();
            }, 100);
        }

        return qrContainer;
    }

    /**
     * 更新 QR Code 元素
     */
    updateQRElement(element, url, size, foreground, background, radius, border) {
        // 生成新的 QR Code
        const qrCanvas = this.generateQRCode(url, size, foreground, background, border);
        if (!qrCanvas) return;

        // 更新數據屬性
        element.dataset.qrUrl = url;
        element.dataset.qrSize = size;
        element.dataset.qrForeground = foreground;
        element.dataset.qrBackground = background;
        element.dataset.qrRadius = radius;
        element.dataset.qrBorder = border;

        // 更新圖片
        const img = element.querySelector('img');
        if (img) {
            img.src = qrCanvas.toDataURL('image/png');
        }

        // 更新尺寸
        element.style.width = size + 'px';
        element.style.height = size + 'px';

        // 更新圓角
        if (radius > 0) {
            img.style.borderRadius = radius + 'px';
            element.style.borderRadius = radius + 'px';
        } else {
            img.style.borderRadius = '0';
            element.style.borderRadius = '0';
        }

        // 重新添加調整大小控制點
        this.removeResizeHandles(element);
        this.addResizeHandles(element);
    }

    isValidUrl(string) {
        try {
            const url = new URL(string);
            return url.protocol === 'http:' || url.protocol === 'https:';
        } catch (_) {
            return false;
        }
    }
    

    


    
    setupQRInteractions(qrElement) {
        // Double-click to edit URL
        qrElement.addEventListener('dblclick', (e) => {
            e.stopPropagation();
            const currentUrl = qrElement.dataset.qrUrl || '';
            const newUrl = prompt(
                this.posterMaker.currentLanguage === 'en'
                    ? 'Enter new URL for QR code:'
                    : '輸入QR碼的新網址:',
                currentUrl
            );

            if (newUrl && this.isValidUrl(newUrl)) {
                const size = parseInt(qrElement.dataset.qrSize) || 90;
                const foreground = qrElement.dataset.qrForeground || '#00ffff';
                const background = qrElement.dataset.qrBackground || '#000000';
                const radius = parseInt(qrElement.dataset.qrRadius) || 16;
                const border = parseInt(qrElement.dataset.qrBorder) || 1;

                this.updateQRElement(qrElement, newUrl, size, foreground, background, radius, border);
            }
        });

        // Context menu for QR options
        qrElement.addEventListener('contextmenu', (e) => {
            e.preventDefault();
            this.showQRContextMenu(e, qrElement);
        });
    }

    removeResizeHandles(element) {
        const handles = element.querySelectorAll('.kms-resize-handle');
        handles.forEach(handle => handle.remove());
    }



    addResizeHandles(element) {
        const handles = ['nw', 'ne', 'sw', 'se'];
        
        handles.forEach(direction => {
            const handle = document.createElement('div');
            handle.className = `kms-resize-handle ${direction}`;
            handle.addEventListener('mousedown', (e) => {
                e.stopPropagation();
                this.startResize(e, element, direction);
            });
            element.appendChild(handle);
        });
    }
    
    startResize(e, element, direction) {
        e.preventDefault();
        
        const startX = e.clientX;
        const startY = e.clientY;
        const startSize = parseInt(window.getComputedStyle(element).width);
        const startLeft = parseInt(element.style.left);
        const startTop = parseInt(element.style.top);
        
        const canvas = document.getElementById('posterCanvas');
        
        const handleResize = (e) => {
            const deltaX = e.clientX - startX;
            const deltaY = e.clientY - startY;
            
            // Use the larger delta to maintain square aspect ratio
            const delta = Math.max(Math.abs(deltaX), Math.abs(deltaY)) * 
                         (deltaX < 0 || deltaY < 0 ? -1 : 1);
            
            let newSize = startSize;
            let newLeft = startLeft;
            let newTop = startTop;
            
            switch (direction) {
                case 'se':
                    newSize = Math.max(50, Math.min(300, startSize + delta));
                    break;
                case 'sw':
                    newSize = Math.max(50, Math.min(300, startSize - delta));
                    newLeft = startLeft + (startSize - newSize);
                    break;
                case 'ne':
                    newSize = Math.max(50, Math.min(300, startSize + delta));
                    newTop = startTop + (startSize - newSize);
                    break;
                case 'nw':
                    newSize = Math.max(50, Math.min(300, startSize - delta));
                    newLeft = startLeft + (startSize - newSize);
                    newTop = startTop + (startSize - newSize);
                    break;
            }
            
            // Constrain to canvas bounds
            newLeft = Math.max(0, Math.min(newLeft, canvas.offsetWidth - newSize));
            newTop = Math.max(0, Math.min(newTop, canvas.offsetHeight - newSize));
            
            // Apply new dimensions
            element.style.width = newSize + 'px';
            element.style.height = newSize + 'px';
            element.style.left = newLeft + 'px';
            element.style.top = newTop + 'px';
            
            const img = element.querySelector('img');
            if (img) {
                img.style.width = newSize + 'px';
                img.style.height = newSize + 'px';
            }
        };
        
        const stopResize = () => {
            document.removeEventListener('mousemove', handleResize);
            document.removeEventListener('mouseup', stopResize);
        };
        
        document.addEventListener('mousemove', handleResize);
        document.addEventListener('mouseup', stopResize);
    }
    
    showQRContextMenu(e, qrElement) {
        // Remove existing context menu
        const existingMenu = document.querySelector('.kms-context-menu');
        if (existingMenu) {
            existingMenu.remove();
        }
        
        const menu = document.createElement('div');
        menu.className = 'kms-context-menu';
        menu.style.position = 'fixed';
        menu.style.left = e.clientX + 'px';
        menu.style.top = e.clientY + 'px';
        menu.style.background = 'white';
        menu.style.border = '1px solid #ccc';
        menu.style.borderRadius = '4px';
        menu.style.boxShadow = '0 2px 10px rgba(0,0,0,0.1)';
        menu.style.zIndex = '1000';
        menu.style.minWidth = '150px';
        
        const options = [
            {
                text: this.posterMaker.currentLanguage === 'en' ? 'Edit URL' : '編輯網址',
                action: () => {
                    const currentUrl = qrElement.dataset.qrUrl || '';
                    const newUrl = prompt(
                        this.posterMaker.currentLanguage === 'en' 
                            ? 'Enter new URL for QR code:'
                            : '輸入QR碼的新網址:',
                        currentUrl
                    );
                    
                    if (newUrl && this.isValidUrl(newUrl)) {
                        this.regenerateQRCode(qrElement, newUrl);
                    }
                }
            },
            {
                text: this.posterMaker.currentLanguage === 'en' ? 'Change Color' : '更改顏色',
                action: () => this.showQRColorOptions(qrElement)
            },
            {
                text: this.posterMaker.currentLanguage === 'en' ? 'Delete QR Code' : '刪除QR碼',
                action: () => {
                    qrElement.remove();
                    this.posterMaker.elements = this.posterMaker.elements.filter(el => el !== qrElement);
                    this.posterMaker.selectedElement = null;
                }
            }
        ];
        
        options.forEach(option => {
            const item = document.createElement('div');
            item.textContent = option.text;
            item.style.padding = '8px 12px';
            item.style.cursor = 'pointer';
            item.style.borderBottom = '1px solid #eee';
            item.addEventListener('click', () => {
                option.action();
                menu.remove();
            });
            item.addEventListener('mouseenter', () => {
                item.style.backgroundColor = '#f0f0f0';
            });
            item.addEventListener('mouseleave', () => {
                item.style.backgroundColor = 'white';
            });
            menu.appendChild(item);
        });
        
        document.body.appendChild(menu);
        
        // Remove menu when clicking elsewhere
        setTimeout(() => {
            document.addEventListener('click', () => {
                menu.remove();
            }, { once: true });
        }, 100);
    }
    
    showQRColorOptions(qrElement) {
        const colors = [
            { name: 'Black', dark: '#000000', light: '#FFFFFF' },
            { name: 'Blue', dark: '#0066CC', light: '#FFFFFF' },
            { name: 'Red', dark: '#CC0000', light: '#FFFFFF' },
            { name: 'Green', dark: '#006600', light: '#FFFFFF' },
            { name: 'Purple', dark: '#6600CC', light: '#FFFFFF' }
        ];
        
        const colorName = prompt(
            this.posterMaker.currentLanguage === 'en' 
                ? `Choose color: ${colors.map(c => c.name).join(', ')}`
                : `選擇顏色: ${colors.map(c => c.name).join(', ')}`,
            'Black'
        );
        
        const selectedColor = colors.find(c => 
            c.name.toLowerCase() === colorName.toLowerCase()
        );
        
        if (selectedColor) {
            this.regenerateQRCodeWithColor(qrElement, selectedColor);
        }
    }
    
    regenerateQRCodeWithColor(qrElement, colorOptions) {
        const url = qrElement.dataset.qrUrl;
        if (!url) return;

        try {
            // Create QR code using the same method as KMS_Logo_Photo.app
            const qr = qrcode(0, 'M');
            qr.addData(url);
            qr.make();

            // Create canvas and draw QR code with custom colors
            const canvas = document.createElement('canvas');
            const size = 200;
            const cellSize = Math.floor(size / qr.getModuleCount());
            const actualSize = cellSize * qr.getModuleCount();

            canvas.width = actualSize;
            canvas.height = actualSize;

            const ctx = canvas.getContext('2d');

            // Fill background with light color
            ctx.fillStyle = colorOptions.light || '#FFFFFF';
            ctx.fillRect(0, 0, actualSize, actualSize);

            // Draw QR code modules with dark color
            ctx.fillStyle = colorOptions.dark || '#000000';
            for (let row = 0; row < qr.getModuleCount(); row++) {
                for (let col = 0; col < qr.getModuleCount(); col++) {
                    if (qr.isDark(row, col)) {
                        ctx.fillRect(col * cellSize, row * cellSize, cellSize, cellSize);
                    }
                }
            }

            const dataUrl = canvas.toDataURL('image/png');
            const img = qrElement.querySelector('img');
            if (img) {
                img.src = dataUrl;
            }
        } catch (error) {
            console.error('QR Code color change error:', error);
        }
    }
    
    // Update controls when a QR element is selected
    updateControlsForQRElement(element) {
        // 不再直接操作控制面板的顯示/隱藏，讓統一的控制面板系統處理
        // 只更新控制項的值
        
        // Update controls with current QR settings
        const urlInput = document.getElementById('qrUrl');
        const sizeInput = document.getElementById('qrSize');
        const foregroundInput = document.getElementById('qrForeground');
        const backgroundInput = document.getElementById('qrBackground');
        const radiusInput = document.getElementById('qrRadius');
        const borderInput = document.getElementById('qrBorder');

        if (urlInput && element.dataset.qrUrl) {
            urlInput.value = element.dataset.qrUrl;
        }

        // Load stored options
        const storedOptions = element.dataset.qrOptions;
        if (storedOptions) {
            try {
                const options = JSON.parse(storedOptions);
                if (sizeInput) sizeInput.value = options.size || 90;
                if (foregroundInput) foregroundInput.value = options.foreground || '#00ffff';
                if (backgroundInput) backgroundInput.value = options.background || '#000000';
                if (radiusInput) radiusInput.value = options.radius || 16;
                if (borderInput) borderInput.value = options.border || 1;
            } catch (e) {
                console.error('Error parsing QR options:', e);
            }
        }
        
        console.log('QR控制面板已更新');
    }
    
    // Trading card support methods
    checkAndApplyToTradingCards(qrElement) {
        // Check if trading card mode is active
        const tradingCardSection = document.getElementById('tradingCardLayoutSection');
        if (tradingCardSection && !tradingCardSection.classList.contains('hidden') && window.kmsTradingCard) {
            // Auto-apply QR code to all trading cards when created
            setTimeout(() => {
                this.applyQRToTradingCards(qrElement);
            }, 100);
        }
    }
    
    applyQRToTradingCards(qrElement) {
        if (!window.kmsTradingCard || !qrElement) return;
        
        const qrUrl = qrElement.dataset.qrUrl;
        if (!qrUrl) return;
        
        window.kmsTradingCard.applyQRToAllCards(qrUrl);
    }
    
    // Method to manually apply current canvas QR code to trading cards
    applyCanvasQRToTradingCards() {
        if (!window.kmsTradingCard) return;
        
        const canvas = document.getElementById('posterCanvas');
        const qrElements = canvas.querySelectorAll('.kms-qr-element');
        
        if (qrElements.length === 0) {
            window.kmsTradingCard.showMessage('請先在畫布上添加 QR Code');
            return;
        }
        
        // Apply the first QR element or selected QR element
        const selectedElement = this.posterMaker.selectedElement;
        const targetElement = (selectedElement && selectedElement.classList.contains('kms-qr-element')) 
            ? selectedElement 
            : qrElements[0];
            
        this.applyQRToTradingCards(targetElement);
    }

    initializeRotationButton() {
        const rotateBtn = document.getElementById('rotateQRBtn');
        if (rotateBtn) {
            rotateBtn.addEventListener('click', () => this.rotateSelectedElement());
        }
    }

    rotateSelectedElement() {
        if (!this.posterMaker.selectedElement) return;

        const element = this.posterMaker.selectedElement;

        // 獲取當前旋轉角度
        let currentRotation = 0;
        const transform = element.style.transform;
        const rotateMatch = transform.match(/rotate\((-?\d+)deg\)/);
        if (rotateMatch) {
            currentRotation = parseInt(rotateMatch[1]);
        }

        // 增加 90 度
        const newRotation = (currentRotation + 90) % 360;

        // 應用新的旋轉
        const otherTransforms = transform.replace(/rotate\(-?\d+deg\)\s*/g, '').trim();
        element.style.transform = `${otherTransforms} rotate(${newRotation}deg)`.trim();

        if (this.posterMaker && this.posterMaker.showMessage) {
            this.posterMaker.showMessage(`QR Code 已旋轉到 ${newRotation}°`);
        }
    }
}
