<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>KMS Poster Maker</title>
    
    <!-- CSS Files -->
    <link rel="stylesheet" href="css/main.css">
    <link rel="stylesheet" href="css/poster-canvas.css">
    <link rel="stylesheet" href="css/controls.css">
    <link rel="stylesheet" href="css/file-manager.css">
    <link rel="stylesheet" href="css/trading-card.css">
    <link rel="stylesheet" href="css/print.css">
    
    <!-- Font Awesome for icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- QR Code Library - Same as KMS_Logo_Photo.app -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/qrcode-generator/1.4.4/qrcode.min.js"></script>
</head>
<body>
    <!-- Header -->
    <header class="kms-header">
        <div class="kms-logo">
            <span data-translate="title">KMS Poster Maker</span>
        </div>
        <div class="kms-header-actions">
            <button id="printBtn" class="kms-header-print-btn">
                <i class="fas fa-print"></i>
                <span data-translate="print">Print Poster</span>
            </button>
            <button id="languageToggle" class="kms-language-toggle">中文</button>
        </div>
    </header>

    <!-- Main Container -->
    <div class="kms-container">


        <div class="kms-main">
            <!-- Sidebar Controls -->
            <aside class="kms-sidebar">
                <div class="kms-controls-panel">


                    <!-- File Management Section -->
                    <section class="kms-controls-section">
                        <h3 class="kms-section-title">
                            <i class="fas fa-save"></i>
                            <span data-translate="fileManagement" data-en="File Management" data-zh="檔案管理">File Management</span>
                        </h3>
                        <div class="kms-form-group">
                            <button id="savePosterBtn" class="kms-btn" style="width: 100%; margin-bottom: 0.5rem;">
                                <i class="fas fa-save"></i>
                                <span data-translate="savePoster" data-en="Save Poster" data-zh="儲存海報">Save Poster</span>
                            </button>
                            <button id="loadPosterBtn" class="kms-btn kms-btn-secondary" style="width: 100%; margin-bottom: 0.5rem;">
                                <i class="fas fa-folder-open"></i>
                                <span data-translate="loadPoster" data-en="Load Poster" data-zh="載入海報">Load Poster</span>
                            </button>
                            <button id="manageSavedBtn" class="kms-btn kms-btn-secondary" style="width: 100%; margin-bottom: 0.5rem;">
                                <i class="fas fa-list"></i>
                                <span data-translate="manageSaved" data-en="Manage Saved" data-zh="管理已儲存">Manage Saved</span>
                            </button>
                            <button id="refreshPageBtn" class="kms-btn kms-btn-warning" style="width: 100%;">
                                <i class="fas fa-refresh"></i>
                                <span data-translate="refreshPage" data-en="Refresh Page" data-zh="重新整理">Refresh Page</span>
                            </button>
                        </div>
                    </section>

                    <!-- Quick Colors Section -->
                    <section class="kms-controls-section kms-quick-colors-section">
                        <h3 class="kms-section-title">
                            <i class="fas fa-palette"></i>
                            <span data-translate="quickColors" data-en="Quick Colors" data-zh="快速顏色">Quick Colors</span>
                        </h3>
                        
                        <!-- Color Target Buttons -->
                        <div class="kms-color-target-buttons">
                            <button class="kms-color-target-btn active" data-target="text">
                                <i class="fas fa-font"></i>
                                <span data-translate="textColor" data-en="Text" data-zh="文字">Text</span>
                            </button>
                            <button class="kms-color-target-btn" data-target="border">
                                <i class="fas fa-border-style"></i>
                                <span data-translate="textBorder" data-en="Border" data-zh="邊框">Border</span>
                            </button>
                            <button class="kms-color-target-btn" data-target="background">
                                <i class="fas fa-fill"></i>
                                <span data-translate="textBackground" data-en="Background" data-zh="背景">Background</span>
                            </button>
                            <button class="kms-color-target-btn" data-target="shadow">
                                <i class="fas fa-adjust"></i>
                                <span data-translate="textShadow" data-en="Shadow" data-zh="陰影">Shadow</span>
                            </button>
                            <button class="kms-color-target-btn" data-target="imageBorder">
                                <i class="fas fa-image"></i>
                                <span data-translate="imageBorder" data-en="Image" data-zh="圖片">Image</span>
                            </button>
                        </div>

                        <!-- Color Palette -->
                        <div class="kms-color-palette">
                            <div class="kms-color-palette-header">
                                <span class="kms-current-target-label">
                                    <i class="fas fa-font"></i>
                                    <span data-translate="applyingTo" data-en="Applying to:" data-zh="應用到：">Applying to:</span>
                                    <span class="kms-current-target-name" data-translate="textColor" data-en="Text Color" data-zh="文字顏色">Text Color</span>
                                </span>
                            </div>
                            <div class="kms-quick-colors-grid">
                                <!-- 第一排：白到淺灰漸變 -->
                                <button class="kms-quick-color-btn" data-color="#FFFFFF" style="background-color: #FFFFFF; border: 2px solid #ddd;" title="White">
                                    <span class="kms-color-tooltip">White</span>
                                </button>
                                <button class="kms-quick-color-btn" data-color="#F5F5F5" style="background-color: #F5F5F5;" title="Light Gray">
                                    <span class="kms-color-tooltip">Light Gray</span>
                                </button>
                                <button class="kms-quick-color-btn" data-color="#E0E0E0" style="background-color: #E0E0E0;" title="Silver">
                                    <span class="kms-color-tooltip">Silver</span>
                                </button>
                                <button class="kms-quick-color-btn" data-color="#CCCCCC" style="background-color: #CCCCCC;" title="Light Gray">
                                    <span class="kms-color-tooltip">Light Gray</span>
                                </button>
                                <button class="kms-quick-color-btn" data-color="#B8B8B8" style="background-color: #B8B8B8;" title="Gray">
                                    <span class="kms-color-tooltip">Gray</span>
                                </button>
                                <button class="kms-quick-color-btn" data-color="#A0A0A0" style="background-color: #A0A0A0;" title="Gray">
                                    <span class="kms-color-tooltip">Gray</span>
                                </button>
                                <button class="kms-quick-color-btn" data-color="#808080" style="background-color: #808080;" title="Gray">
                                    <span class="kms-color-tooltip">Gray</span>
                                </button>
                                <button class="kms-quick-color-btn" data-color="#606060" style="background-color: #606060;" title="Dark Gray">
                                    <span class="kms-color-tooltip">Dark Gray</span>
                                </button>
                                <button class="kms-quick-color-btn" data-color="#404040" style="background-color: #404040;" title="Dark Gray">
                                    <span class="kms-color-tooltip">Dark Gray</span>
                                </button>
                                <button class="kms-quick-color-btn" data-color="#000000" style="background-color: #000000;" title="Black">
                                    <span class="kms-color-tooltip">Black</span>
                                </button>
                                
                                <!-- 第二排：紅色系列 -->
                                <button class="kms-quick-color-btn" data-color="#FF0000" style="background-color: #FF0000;" title="Red">
                                    <span class="kms-color-tooltip">Red</span>
                                </button>
                                <button class="kms-quick-color-btn" data-color="#FF4500" style="background-color: #FF4500;" title="Orange Red">
                                    <span class="kms-color-tooltip">Orange Red</span>
                                </button>
                                <button class="kms-quick-color-btn" data-color="#FF6B6B" style="background-color: #FF6B6B;" title="Light Red">
                                    <span class="kms-color-tooltip">Light Red</span>
                                </button>
                                <button class="kms-quick-color-btn" data-color="#DC143C" style="background-color: #DC143C;" title="Crimson">
                                    <span class="kms-color-tooltip">Crimson</span>
                                </button>
                                <button class="kms-quick-color-btn" data-color="#B22222" style="background-color: #B22222;" title="Fire Brick">
                                    <span class="kms-color-tooltip">Fire Brick</span>
                                </button>
                                <button class="kms-quick-color-btn" data-color="#8B0000" style="background-color: #8B0000;" title="Dark Red">
                                    <span class="kms-color-tooltip">Dark Red</span>
                                </button>
                                <button class="kms-quick-color-btn" data-color="#FF69B4" style="background-color: #FF69B4;" title="Hot Pink">
                                    <span class="kms-color-tooltip">Hot Pink</span>
                                </button>
                                <button class="kms-quick-color-btn" data-color="#FF1493" style="background-color: #FF1493;" title="Deep Pink">
                                    <span class="kms-color-tooltip">Deep Pink</span>
                                </button>
                                <button class="kms-quick-color-btn" data-color="#F8BBD9" style="background-color: #F8BBD9;" title="Pink">
                                    <span class="kms-color-tooltip">Pink</span>
                                </button>
                                <button class="kms-quick-color-btn" data-color="#FFB6C1" style="background-color: #FFB6C1;" title="Light Pink">
                                    <span class="kms-color-tooltip">Light Pink</span>
                                </button>
                                
                                <!-- 第三排：橙黃綠色系列 -->
                                <button class="kms-quick-color-btn" data-color="#FFA500" style="background-color: #FFA500;" title="Orange">
                                    <span class="kms-color-tooltip">Orange</span>
                                </button>
                                <button class="kms-quick-color-btn" data-color="#FFB347" style="background-color: #FFB347;" title="Light Orange">
                                    <span class="kms-color-tooltip">Light Orange</span>
                                </button>
                                <button class="kms-quick-color-btn" data-color="#FFFF00" style="background-color: #FFFF00;" title="Yellow">
                                    <span class="kms-color-tooltip">Yellow</span>
                                </button>
                                <button class="kms-quick-color-btn" data-color="#FFD700" style="background-color: #FFD700;" title="Gold">
                                    <span class="kms-color-tooltip">Gold</span>
                                </button>
                                <button class="kms-quick-color-btn" data-color="#FFEAA7" style="background-color: #FFEAA7;" title="Light Yellow">
                                    <span class="kms-color-tooltip">Light Yellow</span>
                                </button>
                                <button class="kms-quick-color-btn" data-color="#ADFF2F" style="background-color: #ADFF2F;" title="Green Yellow">
                                    <span class="kms-color-tooltip">Green Yellow</span>
                                </button>
                                <button class="kms-quick-color-btn" data-color="#32CD32" style="background-color: #32CD32;" title="Lime Green">
                                    <span class="kms-color-tooltip">Lime Green</span>
                                </button>
                                <button class="kms-quick-color-btn" data-color="#00FF00" style="background-color: #00FF00;" title="Lime">
                                    <span class="kms-color-tooltip">Lime</span>
                                </button>
                                <button class="kms-quick-color-btn" data-color="#228B22" style="background-color: #228B22;" title="Forest Green">
                                    <span class="kms-color-tooltip">Forest Green</span>
                                </button>
                                <button class="kms-quick-color-btn" data-color="#96CEB4" style="background-color: #96CEB4;" title="Light Green">
                                    <span class="kms-color-tooltip">Light Green</span>
                                </button>
                                
                                <!-- 第四排：藍紫色系列 -->
                                <button class="kms-quick-color-btn" data-color="#00FFFF" style="background-color: #00FFFF;" title="Cyan">
                                    <span class="kms-color-tooltip">Cyan</span>
                                </button>
                                <button class="kms-quick-color-btn" data-color="#00CED1" style="background-color: #00CED1;" title="Dark Turquoise">
                                    <span class="kms-color-tooltip">Dark Turquoise</span>
                                </button>
                                <button class="kms-quick-color-btn" data-color="#4ECDC4" style="background-color: #4ECDC4;" title="Teal">
                                    <span class="kms-color-tooltip">Teal</span>
                                </button>
                                <button class="kms-quick-color-btn" data-color="#87CEEB" style="background-color: #87CEEB;" title="Sky Blue">
                                    <span class="kms-color-tooltip">Sky Blue</span>
                                </button>
                                <button class="kms-quick-color-btn" data-color="#45B7D1" style="background-color: #45B7D1;" title="Light Blue">
                                    <span class="kms-color-tooltip">Light Blue</span>
                                </button>
                                <button class="kms-quick-color-btn" data-color="#0000FF" style="background-color: #0000FF;" title="Blue">
                                    <span class="kms-color-tooltip">Blue</span>
                                </button>
                                <button class="kms-quick-color-btn" data-color="#4169E1" style="background-color: #4169E1;" title="Royal Blue">
                                    <span class="kms-color-tooltip">Royal Blue</span>
                                </button>
                                <button class="kms-quick-color-btn" data-color="#8A2BE2" style="background-color: #8A2BE2;" title="Blue Violet">
                                    <span class="kms-color-tooltip">Blue Violet</span>
                                </button>
                                <button class="kms-quick-color-btn" data-color="#9932CC" style="background-color: #9932CC;" title="Dark Orchid">
                                    <span class="kms-color-tooltip">Dark Orchid</span>
                                </button>
                                <button class="kms-quick-color-btn" data-color="#DDA0DD" style="background-color: #DDA0DD;" title="Plum">
                                    <span class="kms-color-tooltip">Plum</span>
                                </button>
                            </div>
                        </div>
                    </section>

                    <!-- Paper Size Section -->
                    <section class="kms-controls-section">
                        <h3 class="kms-section-title" data-translate="paperSize">Paper Size</h3>
                        <div class="kms-paper-size-options">
                            <div class="kms-paper-option" data-size="letter">
                                <div class="size-label" data-translate="letter">Letter</div>
                                <div class="size-dimensions">8.5" × 11"</div>
                            </div>
                            <div class="kms-paper-option" data-size="4x6">
                                <div class="size-label" data-translate="size4x6">4" × 6"</div>
                                <div class="size-dimensions">4" × 6"</div>
                                <button class="kms-btn kms-btn-sm" id="rotate4x6Btn" style="margin-top: 0.5rem; font-size: 0.8rem;">
                                    <i class="fas fa-rotate-right"></i>
                                    <span data-translate="rotate" data-en="Rotate" data-zh="旋轉">Rotate</span>
                                </button>
                            </div>
                            <div class="kms-paper-option" data-size="trading-card">
                                <div class="size-label" data-translate="tradingCard">Trading Card</div>
                                <div class="size-dimensions">2.5" × 3.5"</div>
                            </div>
                        </div>
                    </section>

                    <!-- Trading Card Layout Section -->
                    <section class="kms-controls-section" id="tradingCardLayoutSection" style="display: none;">
                        <h3 class="kms-section-title">
                            <i class="fas fa-th"></i>
                            <span data-translate="tradingCardLayout" data-en="Trading Card Layout" data-zh="交易卡佈局">Trading Card Layout</span>
                        </h3>
                        <div class="kms-form-group">
                            <label data-translate="paperType" data-en="Paper Type:" data-zh="紙張類型：">Paper Type:</label>
                            <select id="tradingCardPaper" class="kms-input">
                                <option value="letter" data-translate="letterPaper" data-en="Letter (9 cards)" data-zh="Letter (9張卡片)">Letter (9 cards)</option>
                                <option value="4x6" data-translate="photo4x6Paper" data-en="4×6 (2 cards)" data-zh="4×6 (2張卡片)">4×6 (2 cards)</option>
                            </select>
                        </div>
                        <div class="kms-form-group">
                            <button id="generateTradingCardsBtn" class="kms-btn" style="width: 100%;">
                                <i class="fas fa-magic"></i>
                                <span data-translate="generateCards" data-en="Generate Trading Cards" data-zh="生成交易卡">Generate Trading Cards</span>
                            </button>
                        </div>

                        <!-- 編輯模式選擇 -->
                        <div class="kms-form-group">
                            <label data-translate="editMode" data-en="Edit Mode:" data-zh="編輯模式：">Edit Mode:</label>
                            <div class="kms-radio-group">
                                <label class="kms-radio-option">
                                    <input type="radio" name="editMode" value="single" id="singleEditMode" checked>
                                    <span data-translate="singleEdit" data-en="Single Card Edit" data-zh="單張編輯">Single Card Edit</span>
                                </label>
                                <label class="kms-radio-option">
                                    <input type="radio" name="editMode" value="sync" id="syncEditMode">
                                    <span data-translate="syncEdit" data-en="Multi-Card Sync" data-zh="多張同步">Multi-Card Sync</span>
                                </label>
                            </div>
                        </div>

                        <!-- 同步模式控制 -->
                        <div class="kms-form-group" id="syncModeControls" style="display: none;">
                            <div class="kms-sync-info">
                                <i class="fas fa-info-circle"></i>
                                <span data-translate="syncModeInfo" data-en="In sync mode, edit Card 1 and changes will apply to all cards" data-zh="同步模式下，編輯卡片1，變更會套用到所有卡片">In sync mode, edit Card 1 and changes will apply to all cards</span>
                            </div>
                            <button id="applySyncBtn" class="kms-btn kms-btn-secondary" style="width: 100%;">
                                <i class="fas fa-sync-alt"></i>
                                <span data-translate="syncNow" data-en="Sync Card 1 to All" data-zh="同步卡片1到全部">Sync Card 1 to All</span>
                            </button>
                        </div>

                        <!-- 單張模式控制 -->
                        <div class="kms-form-group" id="singleModeControls">
                            <button id="applyToAllCardsBtn" class="kms-btn kms-btn-secondary" style="width: 100%;">
                                <i class="fas fa-copy"></i>
                                <span data-translate="applyToAll" data-en="Apply Canvas to All Cards" data-zh="套用畫布到所有卡片">Apply Canvas to All Cards</span>
                            </button>
                        </div>
                    </section>

                    <!-- Add Elements Section -->
                    <section class="kms-controls-section">
                        <h3 class="kms-section-title">
                            <i class="fas fa-plus"></i>
                            <span data-translate="addElements">Add Elements</span>
                        </h3>
                        <div class="kms-form-group">
                            <button id="addTextBtn" class="kms-btn" style="width: 100%; margin-bottom: 0.5rem;">
                                <i class="fas fa-font"></i>
                                <span data-translate="addText">Add Text</span>
                            </button>
                            <button id="addImageBtn" class="kms-btn kms-btn-secondary" style="width: 100%; margin-bottom: 0.5rem;">
                                <i class="fas fa-image"></i>
                                <span data-translate="addImage">Add Image</span>
                            </button>
                            <button id="addQRBtn" class="kms-btn kms-btn-secondary" style="width: 100%;">
                                <i class="fas fa-qrcode"></i>
                                <span data-translate="addQR">Add QR Code</span>
                            </button>
                        </div>
                    </section>

                    <!-- Text Controls Section -->
                    <section class="kms-controls-section kms-text-controls-section" id="textControlsSection">
                        <h3 class="kms-section-title" data-translate="textControls">Text Controls</h3>
                        
                        <!-- Font & Basic Settings -->
                        <div class="kms-text-control-group">
                            <div class="kms-control-group-header">
                                <i class="fas fa-font"></i>
                                <span>Font & Style</span>
                            </div>
                            <div class="kms-control-group-content">
                                <div class="kms-form-group">
                                    <label class="kms-form-label" data-translate="font">Font Family</label>
                                    <select id="fontFamily" class="kms-select kms-enhanced-select">
                                        <option value="Arial, sans-serif">Arial</option>
                                        <option value="Times New Roman, serif">Times New Roman</option>
                                        <option value="Helvetica, Arial, sans-serif">Helvetica</option>
                                        <option value="Georgia, serif">Georgia</option>
                                        <option value="Verdana, sans-serif">Verdana</option>
                                        <option value="Courier New, monospace">Courier New</option>
                                        <option value="Impact, sans-serif">Impact</option>
                                        <option value="Comic Sans MS, cursive">Comic Sans MS</option>
                                        <option value="Trebuchet MS, sans-serif">Trebuchet MS</option>
                                        <option value="Palatino, serif">Palatino</option>
                                        <option value="Roboto, sans-serif">Roboto</option>
                                        <option value="Open Sans, sans-serif">Open Sans</option>
                                    </select>
                                </div>

                                <div class="kms-form-group">
                                    <label class="kms-form-label" data-translate="fontSize">Font Size</label>
                                    <div class="kms-range-control">
                                        <input type="range" id="fontSize" class="kms-range kms-enhanced-range" min="12" max="72" value="24">
                                        <div class="kms-range-display">
                                            <span id="fontSizeValue">24px</span>
                                        </div>
                                    </div>
                                </div>

                                <div class="kms-form-group">
                                    <label class="kms-form-label" data-translate="fontColor">Font Color</label>
                                    <div class="kms-enhanced-color-row">
                                        <input type="color" id="fontColor" class="kms-color-picker kms-enhanced-color-picker" value="#333333">
                                        <div class="kms-color-preview kms-enhanced-color-preview" data-color="#333333" style="background-color: #333333;"></div>
                                        <span class="kms-color-value">#333333</span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Text Style & Alignment -->
                        <div class="kms-text-control-group">
                            <div class="kms-control-group-header">
                                <i class="fas fa-text-width"></i>
                                <span>Style & Alignment</span>
                            </div>
                            <div class="kms-control-group-content">
                                <div class="kms-form-group">
                                    <label class="kms-form-label">Text Style</label>
                                    <div class="kms-enhanced-style-group">
                                        <button class="kms-enhanced-style-btn" id="boldBtn" data-style="bold" title="Bold">
                                            <i class="fas fa-bold"></i>
                                            <span>Bold</span>
                                        </button>
                                        <button class="kms-enhanced-style-btn" id="italicBtn" data-style="italic" title="Italic">
                                            <i class="fas fa-italic"></i>
                                            <span>Italic</span>
                                        </button>
                                        <button class="kms-enhanced-style-btn" id="underlineBtn" data-style="underline" title="Underline">
                                            <i class="fas fa-underline"></i>
                                            <span>Underline</span>
                                        </button>
                                    </div>
                                </div>

                                <div class="kms-form-group">
                                    <label class="kms-form-label">Text Alignment</label>
                                    <div class="kms-enhanced-style-group">
                                        <button class="kms-enhanced-style-btn" id="alignLeftBtn" data-align="left" title="Align Left">
                                            <i class="fas fa-align-left"></i>
                                            <span>Left</span>
                                        </button>
                                        <button class="kms-enhanced-style-btn" id="alignCenterBtn" data-align="center" title="Align Center">
                                            <i class="fas fa-align-center"></i>
                                            <span>Center</span>
                                        </button>
                                        <button class="kms-enhanced-style-btn" id="alignRightBtn" data-align="right" title="Align Right">
                                            <i class="fas fa-align-right"></i>
                                            <span>Right</span>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Text Box Dimensions -->
                        <div class="kms-text-control-group">
                            <div class="kms-control-group-header">
                                <i class="fas fa-expand-arrows-alt"></i>
                                <span>Text Box Size</span>
                            </div>
                            <div class="kms-control-group-content">
                                <div class="kms-dual-range-controls">
                                    <div class="kms-dual-range-item">
                                        <label class="kms-form-label-small">Width</label>
                                        <div class="kms-range-control">
                                            <input type="range" id="textWidth" class="kms-range kms-enhanced-range" min="50" max="500" value="200">
                                            <div class="kms-range-display">
                                                <span id="textWidthValue">200px</span>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="kms-dual-range-item">
                                        <label class="kms-form-label-small">Height</label>
                                        <div class="kms-range-control">
                                            <input type="range" id="textHeight" class="kms-range kms-enhanced-range" min="30" max="300" value="50">
                                            <div class="kms-range-display">
                                                <span id="textHeightValue">50px</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Rotation Control -->
                        <div class="kms-text-control-group">
                            <div class="kms-control-group-header">
                                <i class="fas fa-redo"></i>
                                <span>Rotation</span>
                            </div>
                            <div class="kms-control-group-content">
                                <button class="kms-btn kms-btn-secondary" id="rotateTextBtn" style="width: 100%;">
                                    <i class="fas fa-redo"></i>
                                    <span>Rotate 90°</span>
                                </button>
                            </div>
                        </div>

                        <!-- Border & Background -->
                        <div class="kms-text-control-group">
                            <div class="kms-control-group-header">
                                <i class="fas fa-border-style"></i>
                                <span>Border & Background</span>
                            </div>
                            <div class="kms-control-group-content">
                                <div class="kms-dual-range-controls">
                                    <div class="kms-dual-range-item">
                                        <label class="kms-form-label-small" data-translate="borderWidth">Border Width</label>
                                        <div class="kms-range-control">
                                            <input type="range" id="borderWidth" class="kms-range kms-enhanced-range" min="0" max="20" value="0">
                                            <div class="kms-range-display">
                                                <span id="borderWidthValue">0px</span>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="kms-dual-range-item">
                                        <label class="kms-form-label-small" data-translate="borderRadius">Border Radius</label>
                                        <div class="kms-range-control">
                                            <input type="range" id="borderRadius" class="kms-range kms-enhanced-range" min="0" max="60" value="0">
                                            <div class="kms-range-display">
                                                <span id="borderRadiusValue">0px</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="kms-form-group">
                                    <label class="kms-form-label-small">Border Color</label>
                                    <div class="kms-enhanced-color-row">
                                        <input type="color" id="borderColor" class="kms-color-picker kms-enhanced-color-picker" value="#000000">
                                        <div class="kms-color-preview kms-enhanced-color-preview" data-color="#000000" style="background-color: #000000;"></div>
                                        <span class="kms-color-value">#000000</span>
                                    </div>
                                </div>
                                
                                <div class="kms-form-group">
                                    <label class="kms-form-label-small">Background Color</label>
                                    <div class="kms-enhanced-color-row">
                                        <input type="color" id="textBgColor" class="kms-color-picker kms-enhanced-color-picker" value="#ffffff">
                                        <div class="kms-color-preview kms-enhanced-color-preview" data-color="#ffffff" style="background-color: #ffffff;"></div>
                                        <span class="kms-color-value">#ffffff</span>
                                    </div>
                                </div>
                                
                                <div class="kms-form-group">
                                    <label class="kms-form-label-small">Background Opacity</label>
                                    <div class="kms-range-control">
                                        <input type="range" id="textBgOpacity" class="kms-range kms-enhanced-range" min="0" max="100" value="100">
                                        <div class="kms-range-display">
                                            <span id="textBgOpacityValue">100%</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Text Shadow -->
                        <div class="kms-text-control-group">
                            <div class="kms-control-group-header">
                                <i class="fas fa-adjust"></i>
                                <span>Text Shadow</span>
                            </div>
                            <div class="kms-control-group-content">
                                <div class="kms-dual-range-controls">
                                    <div class="kms-dual-range-item">
                                        <label class="kms-form-label-small">Shadow Blur</label>
                                        <div class="kms-range-control">
                                            <input type="range" id="shadowBlur" class="kms-range kms-enhanced-range" min="0" max="20" value="0">
                                            <div class="kms-range-display">
                                                <span id="shadowBlurValue">0px</span>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="kms-dual-range-item">
                                        <label class="kms-form-label-small">Shadow Distance</label>
                                        <div class="kms-range-control">
                                            <input type="range" id="shadowDistance" class="kms-range kms-enhanced-range" min="0" max="10" value="0">
                                            <div class="kms-range-display">
                                                <span id="shadowDistanceValue">0px</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="kms-form-group">
                                    <label class="kms-form-label-small">Shadow Color</label>
                                    <div class="kms-enhanced-color-row">
                                        <input type="color" id="shadowColor" class="kms-color-picker kms-enhanced-color-picker" value="#000000">
                                        <div class="kms-color-preview kms-enhanced-color-preview" data-color="#000000" style="background-color: #000000;"></div>
                                        <span class="kms-color-value">#000000</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </section>

                    <!-- Background Section -->
                    <section class="kms-controls-section">
                        <h3 class="kms-section-title" data-translate="background">Background</h3>
                        <div class="kms-form-group">
                            <label class="kms-form-label">Background Color</label>
                            <div class="kms-color-row">
                                <input type="color" id="bgColor" class="kms-color-picker" value="#FFD700">
                                <div class="kms-color-preview" data-color="#FFD700" style="background-color: #FFD700;"></div>
                            </div>
                        </div>

                        <div class="kms-form-group">
                            <label class="kms-form-label">Paper Corner Radius</label>
                            <input type="range" id="paperRadius" class="kms-range" min="0" max="60" value="24">
                            <div class="kms-font-size-display">
                                <span id="paperRadiusValue">24px</span>
                            </div>
                        </div>
                    </section>



                    <!-- QR Code Section - Same as KMS_Logo_Photo.app -->
                    <section class="kms-controls-section kms-hidden-section" id="qrControlsSection">
                        <h3 class="kms-section-title">
                            <i class="fas fa-qrcode"></i>
                            <span data-translate="qrGenerator">QR Code Generator</span>
                        </h3>

                        <div class="kms-form-group">
                            <input type="url" id="qrUrl" class="kms-qr-url-input" placeholder="https://KelvinKMS.com/" value="https://KelvinKMS.com/">
                        </div>

                        <div class="kms-qr-controls-grid">
                            <div class="kms-qr-control-item">
                                <label class="kms-qr-label" data-translate="size">Size:</label>
                                <div class="kms-qr-size-control">
                                    <input type="number" id="qrSize" class="kms-qr-size-input" value="90" min="50" max="500">
                                    <span class="kms-qr-unit">px</span>
                                </div>
                            </div>

                            <div class="kms-qr-control-item">
                                <label class="kms-qr-label" data-translate="foregroundColor">Foreground:</label>
                                <input type="color" id="qrForeground" class="kms-qr-color-picker" value="#00ffff">
                            </div>

                            <div class="kms-qr-control-item">
                                <label class="kms-qr-label" data-translate="backgroundColor">Background:</label>
                                <input type="color" id="qrBackground" class="kms-qr-color-picker" value="#000000">
                            </div>

                            <div class="kms-qr-control-item">
                                <label class="kms-qr-label" data-translate="borderRadius">Radius:</label>
                                <div class="kms-qr-size-control">
                                    <input type="number" id="qrRadius" class="kms-qr-size-input" value="8" min="0" max="50">
                                    <span class="kms-qr-unit">px</span>
                                </div>
                            </div>

                            <div class="kms-qr-control-item">
                                <label class="kms-qr-label" data-translate="border">Border:</label>
                                <div class="kms-qr-size-control">
                                    <input type="number" id="qrBorder" class="kms-qr-size-input" value="1" min="0" max="10">
                                    <span class="kms-qr-unit" data-translate="cells">cells</span>
                                </div>
                            </div>
                        </div>

                        <div class="kms-form-group">
                            <button id="generateQRBtn" class="kms-qr-generate-btn">
                                <i class="fas fa-qrcode"></i>
                                <span data-translate="addQR">Add QR Code</span>
                            </button>
                        </div>

                        <!-- QR Code Rotation Control -->
                        <div class="kms-form-group">
                            <button class="kms-btn kms-btn-secondary" id="rotateQRBtn" style="width: 100%;">
                                <i class="fas fa-redo"></i>
                                <span>Rotate 90°</span>
                            </button>
                        </div>
                    </section>

                    <!-- Image Upload Section -->
                    <section class="kms-controls-section kms-hidden-section" id="imageControlsSection">
                        <h3 class="kms-section-title" data-translate="imageControls">Image Controls</h3>
                        <div class="kms-image-upload" id="imageUpload">
                            <div class="kms-upload-icon">
                                <i class="fas fa-cloud-upload-alt"></i>
                            </div>
                            <div class="kms-upload-text">
                                <strong data-translate="uploadImage">Click to upload or drag image here</strong><br>
                                <small data-translate="supportedFormats">Supported: JPG, PNG, GIF</small>
                            </div>
                            <input type="file" id="imageInput" accept="image/*" class="kms-hidden-file-input">
                        </div>

                        <!-- Image Rotation Control -->
                        <div class="kms-form-group">
                            <button class="kms-btn kms-btn-secondary" id="rotateImageBtn" style="width: 100%;">
                                <i class="fas fa-redo"></i>
                                <span>Rotate 90°</span>
                            </button>
                        </div>
                    </section>


                </div>
            </aside>

            <!-- Canvas Area -->
            <main class="kms-canvas-area">
                <div class="kms-canvas-container">
                    <div id="posterCanvas" class="kms-poster-canvas">
                        <!-- Canvas grid overlay -->
                        <div class="kms-canvas-grid" id="canvasGrid"></div>
                        <!-- Canvas border overlay -->
                        <div class="kms-canvas-border" id="canvasBorder"></div>
                        <!-- Elements will be added here dynamically -->
                    </div>
                    

                </div>
            </main>

            <!-- Right Side Panel -->
            <aside class="kms-right-panel">
                <!-- Canvas Controls Section -->
                <section class="kms-right-section" id="canvasControlsSection">
                    <h3 class="kms-right-section-title">
                        <i class="fas fa-tools"></i>
                        <span data-translate="canvasControls" data-en="Canvas Controls" data-zh="畫布控制">Canvas Controls</span>
                    </h3>
                    
                    <div class="kms-canvas-control-buttons">
                        <button id="toggleGridBtn" class="kms-canvas-control-btn" title="Toggle Grid" data-en-title="Toggle Grid" data-zh-title="切換網格">
                            <i class="fas fa-th"></i>
                            <span data-translate="toggleGrid" data-en="Toggle Grid" data-zh="切換網格">Toggle Grid</span>
                        </button>
                        
                        <!-- Zoom Slider Control -->
                        <div class="kms-zoom-control">
                            <label class="kms-zoom-label">
                                <i class="fas fa-search"></i>
                                <span data-translate="zoomControl" data-en="Zoom" data-zh="縮放">Zoom</span>
                            </label>
                            <div class="kms-zoom-slider-container">
                                <span class="kms-zoom-icon-left">
                                    <i class="fas fa-search-minus"></i>
                                </span>
                                <input type="range" id="zoomSlider" class="kms-zoom-slider" 
                                       min="50" max="200" value="100" step="5"
                                       title="Zoom Control" data-en-title="Zoom Control" data-zh-title="縮放控制">
                                <span class="kms-zoom-icon-right">
                                    <i class="fas fa-search-plus"></i>
                                </span>
                            </div>
                            <div class="kms-zoom-display">
                                <span id="zoomValue">100%</span>
                            </div>
                        </div>
                    </div>
                </section>
                
                <!-- Layer Controls Section -->
                <section class="kms-right-section" id="rightLayerControlsSection">
                    <h3 class="kms-right-section-title">
                        <i class="fas fa-layer-group"></i>
                        <span data-translate="layerControls" data-en="Layer Controls" data-zh="圖層控制">Layer Controls</span>
                    </h3>

                    <div class="kms-layer-info">
                        <div class="kms-layer-current">
                            <span data-translate="currentLayer" data-en="Current Layer:" data-zh="當前圖層:">Current Layer:</span>
                            <span id="rightCurrentLayerValue">1</span>
                        </div>
                        <div class="kms-layer-total">
                            <span data-translate="totalLayers" data-en="Total Layers:" data-zh="總圖層數:">Total Layers:</span>
                            <span id="rightTotalLayersValue">1</span>
                        </div>
                    </div>

                    <div class="kms-layer-controls">
                        <div class="kms-layer-buttons-row">
                            <button id="rightBringToFrontBtn" class="kms-layer-btn" title="Bring to Front" data-en-title="Bring to Front" data-zh-title="置頂">
                                <i class="fas fa-angle-double-up"></i>
                                <span data-translate="bringToFront" data-en="To Front" data-zh="置頂">To Front</span>
                            </button>
                            <button id="rightBringForwardBtn" class="kms-layer-btn" title="Bring Forward" data-en-title="Bring Forward" data-zh-title="向前">
                                <i class="fas fa-angle-up"></i>
                                <span data-translate="bringForward" data-en="Forward" data-zh="向前">Forward</span>
                            </button>
                        </div>
                        <div class="kms-layer-buttons-row">
                            <button id="rightSendBackwardBtn" class="kms-layer-btn" title="Send Backward" data-en-title="Send Backward" data-zh-title="向後">
                                <i class="fas fa-angle-down"></i>
                                <span data-translate="sendBackward" data-en="Backward" data-zh="向後">Backward</span>
                            </button>
                            <button id="rightSendToBackBtn" class="kms-layer-btn" title="Send to Back" data-en-title="Send to Back" data-zh-title="置底">
                                <i class="fas fa-angle-double-down"></i>
                                <span data-translate="sendToBack" data-en="To Back" data-zh="置底">To Back</span>
                            </button>
                        </div>
                    </div>

                    <div class="kms-layer-list">
                        <div class="kms-layer-list-header">
                            <span data-translate="layerList" data-en="Layer List" data-zh="圖層列表">Layer List</span>
                        </div>
                        <div id="rightLayerListContainer" class="kms-layer-list-container">
                            <!-- Layer items will be dynamically added here -->
                        </div>
                    </div>
                </section>

                <!-- Canvas Borders Section -->
                <section class="kms-right-section">
                    <h3 class="kms-right-section-title" data-translate="borders">Canvas Borders</h3>
                    <div class="kms-canvas-border-options">
                        <div class="kms-canvas-border-option active" data-border="none">
                            <div class="kms-border-name">None</div>
                        </div>
                        <div class="kms-canvas-border-option" data-border="certificate">
                            <div class="kms-border-name">🏆 Premium Gold Certificate</div>
                        </div>
                        <div class="kms-canvas-border-option" data-border="royal">
                            <div class="kms-border-name">👑 Royal Award Triple Lines</div>
                        </div>
                        <div class="kms-canvas-border-option" data-border="ornate">
                            <div class="kms-border-name">✨ Multi-line Gold Award</div>
                        </div>
                        <div class="kms-canvas-border-option" data-border="premium-award">
                            <div class="kms-border-name">🥇 Ultimate Gold Frame</div>
                        </div>
                        <div class="kms-canvas-border-option" data-border="classic-multilayer">
                            <div class="kms-border-name">🏅 Classic Multi-line Certificate</div>
                        </div>
                        <div class="kms-canvas-border-option" data-border="diploma">
                            <div class="kms-border-name">🎓 Academic Diploma</div>
                        </div>
                        <div class="kms-canvas-border-option" data-border="vintage-cert">
                            <div class="kms-border-name">📜 Vintage Certificate</div>
                        </div>
                        <div class="kms-canvas-border-option" data-border="artdeco">
                            <div class="kms-border-name">🎭 Art Deco Style</div>
                        </div>
                        <div class="kms-canvas-border-option" data-border="elegant-frame">
                            <div class="kms-border-name">💎 Elegant Frame</div>
                        </div>
                        <div class="kms-canvas-border-option" data-border="classic-cert">
                            <div class="kms-border-name">🏛️ Classic Certificate</div>
                        </div>
                        <div class="kms-canvas-border-option" data-border="modern-cert">
                            <div class="kms-border-name">🌟 Modern Certificate</div>
                        </div>
                    </div>
                </section>
            </aside>
        </div>
    </div>

    <!-- 上傳進度覆蓋層 -->
    <div id="uploadProgress" class="kms-upload-progress">
        <div class="kms-progress-content">
            <div class="kms-progress-spinner"></div>
            <div data-translate="uploadingImage" data-en="Uploading image..." data-zh="正在上傳圖片...">Uploading image...</div>
        </div>
    </div>

    <!-- JavaScript Files -->
    <script src="js/layer-manager.js"></script>
    <script src="js/poster-data.js"></script>
    <script src="js/print-preview.js"></script>
    <script src="js/print-core.js"></script>
    <script src="js/text-handler.js"></script>
    <script src="js/image-handler.js"></script>
    <script src="js/qr-generator.js"></script>
    <script src="js/drag-drop.js"></script>
    <script src="js/canvas-controls.js"></script>
    <script src="js/quick-colors.js"></script>
    <script src="js/trading-card.js"></script>
    <script src="js/main-core.js"></script>
    <script src="js/file-manager.js"></script>
</body>
</html>
