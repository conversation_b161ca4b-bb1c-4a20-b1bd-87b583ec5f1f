/* <PERSON><PERSON> Poster Maker - Main Styles */
/* 主要樣式文件 */

:root {
    --color-1: rgb(0, 200, 255);
    --color-2: rgb(255, 200, 0);
    --color-3: rgb(47, 255, 92);
    --color-4: rgb(255, 49, 49);
    --color-5: rgb(0, 255, 234);
    --border-color-1: rgb(0, 162, 255);
    --border-color-2: rgb(255, 180, 19);
    --border-color-3: rgb(30, 255, 0);
    --border-color-4: rgb(255, 63, 63);
    --border-color-5: rgb(12, 255, 243);
    --text-color-1: rgb(0, 0, 0);
    --text-color-2: rgb(255, 255, 255);
    --text-color-3: rgb(0, 255, 128);
    --text-color-4: rgb(255, 128, 0);
    --text-color-5: rgb(255, 0, 128);
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: '<PERSON><PERSON><PERSON>', Tahoma, Geneva, Verdana, sans-serif;
    background-color: #f5f5f5;
    color: #333;
    overflow-x: hidden;
}

.kms-container {
    display: flex;
    height: 100vh;
    max-width: 2000px;
    margin: 0 auto;
}

/* Header */
.kms-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 1rem 2rem;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
    height: 70px;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.kms-logo {
    font-size: 1.5rem;
    font-weight: bold;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.kms-logo::before {
    content: "🎨";
    font-size: 1.8rem;
}

.kms-header-actions {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.kms-language-toggle {
    background: rgba(255,255,255,0.2);
    border: 1px solid rgba(255,255,255,0.3);
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.kms-language-toggle:hover {
    background: rgba(255,255,255,0.3);
}

/* Header Print Button */
.kms-header-print-btn {
    background: linear-gradient(135deg, #28a745, #20c997);
    border: 1px solid rgba(255,255,255,0.3);
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    cursor: pointer;
    transition: all 0.3s ease;
    margin-right: 1rem;
    font-size: 0.9rem;
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.kms-header-print-btn:hover {
    background: linear-gradient(135deg, #218838, #1ea080);
    transform: translateY(-1px);
}



/* Main Content Area */
.kms-main {
    display: flex !important;
    margin-top: 70px;
    height: calc(100vh - 70px);
    max-width: 2000px;
    margin-left: auto;
    margin-right: auto;
    width: 100%;
    flex-wrap: nowrap;
}

/* Sidebar */
.kms-sidebar {
    width: 576px;
    background: white;
    border-right: 1px solid #e0e0e0;
    overflow-y: auto;
    box-shadow: 2px 0 10px rgba(0,0,0,0.05);
    flex-shrink: 0;
}

/* Canvas Area */
.kms-canvas-area {
    flex: 1 1 auto !important;
    display: flex !important;
    flex-direction: column;
    background: #f8f9fa;
    position: relative;
    min-width: 600px;
    max-width: none;
    height: 100%;
    overflow: visible;
}

/* Right Panel */
.kms-right-panel {
    width: 375px;
    background: white;
    border-left: 1px solid #e0e0e0;
    overflow-y: auto;
    box-shadow: -2px 0 10px rgba(0,0,0,0.05);
    flex-shrink: 0;
    padding: 1rem;
}

.kms-right-section {
    margin-bottom: 2rem;
    background: #f8f9fa;
    border-radius: 12px;
    padding: 1.5rem;
    box-shadow: 0 2px 8px rgba(0,0,0,0.05);
}

.kms-right-section-title {
    font-size: 1.1rem;
    font-weight: 600;
    color: #495057;
    margin-bottom: 1rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding-bottom: 0.5rem;
    border-bottom: 2px solid #e9ecef;
}

.kms-right-section-title i {
    color: #667eea;
    font-size: 1.2rem;
}

/* Canvas Control Buttons */
.kms-canvas-control-buttons {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
}

.kms-canvas-control-btn {
    background: var(--color-1);
    color: var(--text-color-2);
    border: 2px solid var(--border-color-1);
    padding: 0.75rem 1rem;
    border-radius: 8px;
    cursor: pointer;
    font-size: 0.9rem;
    font-weight: 500;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    width: 100%;
    justify-content: flex-start;
}

.kms-canvas-control-btn:hover {
    background: var(--color-2);
    border-color: var(--border-color-2);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
}

.kms-canvas-control-btn:active {
    transform: translateY(0);
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

/* Zoom Slider Control */
.kms-zoom-control {
    background: var(--color-1);
    border: 2px solid var(--border-color-1);
    border-radius: 8px;
    padding: 1rem;
    margin-top: 0.75rem;
}

.kms-zoom-label {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: var(--text-color-2);
    font-weight: 500;
    font-size: 0.9rem;
    margin-bottom: 0.75rem;
}

.kms-zoom-label i {
    font-size: 1rem;
}

.kms-zoom-slider-container {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    margin-bottom: 0.5rem;
}

.kms-zoom-icon-left,
.kms-zoom-icon-right {
    color: var(--text-color-2);
    font-size: 0.9rem;
    opacity: 0.8;
    transition: opacity 0.3s ease;
}

.kms-zoom-icon-left:hover,
.kms-zoom-icon-right:hover {
    opacity: 1;
}

.kms-zoom-slider {
    flex: 1;
    height: 6px;
    border-radius: 3px;
    background: linear-gradient(90deg, var(--color-3) 0%, var(--color-2) 100%);
    outline: none;
    -webkit-appearance: none;
    cursor: pointer;
    transition: all 0.3s ease;
}

.kms-zoom-slider::-webkit-slider-thumb {
    -webkit-appearance: none;
    appearance: none;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background: var(--text-color-2);
    border: 2px solid var(--border-color-3);
    cursor: pointer;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.2);
    transition: all 0.3s ease;
}

.kms-zoom-slider::-webkit-slider-thumb:hover {
    transform: scale(1.2);
    box-shadow: 0 3px 8px rgba(0, 0, 0, 0.3);
}

.kms-zoom-slider::-moz-range-thumb {
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background: var(--text-color-2);
    border: 2px solid var(--border-color-3);
    cursor: pointer;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.2);
    transition: all 0.3s ease;
}

.kms-zoom-slider::-moz-range-thumb:hover {
    transform: scale(1.2);
    box-shadow: 0 3px 8px rgba(0, 0, 0, 0.3);
}

.kms-zoom-display {
    text-align: center;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 4px;
    padding: 0.25rem 0.5rem;
}

.kms-zoom-display span {
    color: var(--text-color-2);
    font-weight: 600;
    font-size: 0.85rem;
}

.kms-canvas-control-btn i {
    font-size: 1rem;
    min-width: 16px;
}

.kms-canvas-control-btn.active {
    background: var(--color-3);
    border-color: var(--border-color-3);
}

/* Utility Classes */
.kms-btn {
    background: #667eea;
    color: white;
    border: none;
    padding: 0.75rem 1.5rem;
    border-radius: 8px;
    cursor: pointer;
    font-size: 0.9rem;
    font-weight: 500;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
}

.kms-btn:hover {
    background: #5a67d8;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

.kms-btn-secondary {
    background: #6c757d;
}

.kms-btn-secondary:hover {
    background: #5a6268;
}

.kms-btn-success {
    background: #28a745;
}

.kms-btn-success:hover {
    background: #218838;
}

.kms-btn-danger {
    background: #dc3545;
}

.kms-btn-danger:hover {
    background: #c82333;
}

.kms-input {
    border: 2px solid #e0e0e0;
    border-radius: 8px;
    padding: 0.75rem;
    font-size: 0.9rem;
    transition: border-color 0.3s ease;
    width: 100%;
}

.kms-input:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.kms-select {
    border: 2px solid #e0e0e0;
    border-radius: 8px;
    padding: 0.75rem;
    font-size: 0.9rem;
    background: white;
    cursor: pointer;
    width: 100%;
}

.kms-select:focus {
    outline: none;
    border-color: #667eea;
}

/* Form Groups */
.kms-form-group {
    margin-bottom: 1.5rem;
}

.kms-form-label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 600;
    color: #495057;
    font-size: 0.9rem;
}

/* Color Picker */
.kms-color-picker {
    width: 50px;
    height: 40px;
    border: 2px solid #e0e0e0;
    border-radius: 8px;
    cursor: pointer;
    background: none;
    padding: 0;
}

/* Range Slider */
.kms-range {
    width: 100%;
    height: 6px;
    border-radius: 3px;
    background: #e0e0e0;
    outline: none;
    -webkit-appearance: none;
}

.kms-range::-webkit-slider-thumb {
    -webkit-appearance: none;
    appearance: none;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background: #667eea;
    cursor: pointer;
    box-shadow: 0 2px 6px rgba(0,0,0,0.2);
}

.kms-range::-moz-range-thumb {
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background: #667eea;
    cursor: pointer;
    border: none;
    box-shadow: 0 2px 6px rgba(0,0,0,0.2);
}

/* Precision Mode Hint */
.precision-mode-hint {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: linear-gradient(135deg, var(--color-1), var(--color-5));
    color: var(--text-color-2);
    padding: 15px 25px;
    border-radius: 12px;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
    z-index: 10000;
    font-size: 0.9rem;
    font-weight: 500;
    border: 2px solid rgba(255, 255, 255, 0.2);
    backdrop-filter: blur(10px);
    animation: precisionHintFadeIn 0.3s ease-out;
}

.hint-content {
    display: flex;
    align-items: center;
    gap: 10px;
}

.hint-icon {
    font-size: 1.2rem;
    animation: precisionIconPulse 2s ease-in-out infinite;
}

.hint-text {
    white-space: nowrap;
}

@keyframes precisionHintFadeIn {
    from {
        opacity: 0;
        transform: translate(-50%, -50%) scale(0.8);
    }
    to {
        opacity: 1;
        transform: translate(-50%, -50%) scale(1);
    }
}

@keyframes precisionIconPulse {
    0%, 100% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.1);
    }
}

/* 上傳進度樣式 */
.kms-upload-progress {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    display: none;
    justify-content: center;
    align-items: center;
    z-index: 9999;
}

.kms-progress-content {
    background-color: var(--text-color-2);
    padding: 2rem;
    border-radius: 10px;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 1rem;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
}

.kms-progress-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid var(--color-1);
    border-top: 4px solid transparent;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Enhanced Toast Notification Animations */
@keyframes slideInRight {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

@keyframes slideOutRight {
    from {
        transform: translateX(0);
        opacity: 1;
    }
    to {
        transform: translateX(100%);
        opacity: 0;
    }
}

@keyframes bounceIn {
    0% {
        transform: scale(0.3) translateX(100%);
        opacity: 0;
    }
    50% {
        transform: scale(1.05) translateX(0);
        opacity: 1;
    }
    70% {
        transform: scale(0.9) translateX(0);
    }
    100% {
        transform: scale(1) translateX(0);
        opacity: 1;
    }
}

@keyframes pulse {
    0% {
        box-shadow: 0 6px 20px rgba(0,0,0,0.2);
    }
    50% {
        box-shadow: 0 8px 25px rgba(0,0,0,0.3);
    }
    100% {
        box-shadow: 0 6px 20px rgba(0,0,0,0.2);
    }
}

/* Toast Notification Styles */
.kms-toast {
    position: fixed;
    top: 100px;
    right: 20px;
    padding: 1rem 1.5rem;
    border-radius: 8px;
    color: var(--text-color-2);
    font-weight: 500;
    z-index: 10000;
    transform: translateX(100%);
    transition: transform 0.3s ease;
    max-width: 300px;
    word-wrap: break-word;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    backdrop-filter: blur(10px);
}

.kms-toast-show {
    transform: translateX(0);
}

.kms-toast:hover {
    transform: translateY(-2px);
    animation: pulse 2s infinite;
}

.kms-toast-success {
    background: linear-gradient(135deg, var(--color-3), #20c997);
    border-left: 4px solid var(--border-color-3);
}

.kms-toast-error {
    background: linear-gradient(135deg, var(--color-4), #e74c3c);
    border-left: 4px solid var(--border-color-4);
}

.kms-toast-warning {
    background: linear-gradient(135deg, var(--color-2), #f39c12);
    border-left: 4px solid var(--border-color-2);
}

.kms-toast-info {
    background: linear-gradient(135deg, var(--color-1), #3498db);
    border-left: 4px solid var(--border-color-1);
}

.kms-toast-storage {
    background: linear-gradient(135deg, var(--color-5), #17a2b8);
    border-left: 4px solid var(--border-color-5);
}

/* Progress Toast Styles */
.kms-progress-toast {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    backdrop-filter: blur(10px);
    transition: all 0.3s ease;
}

.kms-progress-toast:hover {
    transform: translateY(-2px);
}

/* Storage Manager Modal Enhancements */
.kms-storage-manager-modal {
    backdrop-filter: blur(5px);
    animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

.kms-storage-manager-modal > div {
    animation: slideInUp 0.4s cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

@keyframes slideInUp {
    from {
        transform: translateY(50px);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

/* Enhanced Button Hover Effects */
.kms-storage-manager-modal button:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.2);
    transition: all 0.2s ease;
}

.kms-storage-manager-modal button:active {
    transform: translateY(0);
    box-shadow: 0 2px 6px rgba(0,0,0,0.2);
}

/* Toast Action Button Styles */
.kms-toast button {
    transition: all 0.2s ease;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.kms-toast button:hover {
    transform: scale(1.05);
    box-shadow: 0 2px 8px rgba(0,0,0,0.2);
}

.kms-toast button:active {
    transform: scale(0.98);
}

/* Close Button Enhancement */
.kms-toast button[style*="position: absolute"] {
    border-radius: 50%;
    transition: all 0.2s ease;
}

.kms-toast button[style*="position: absolute"]:hover {
    background: rgba(255,255,255,0.2);
    transform: rotate(90deg) scale(1.1);
}

/* Progress Bar Animation */
@keyframes progressFill {
    from {
        width: 0%;
    }
    to {
        width: var(--progress-width, 0%);
    }
}

/* Responsive Design */
@media (max-width: 1200px) {
    .kms-sidebar {
        width: 400px;
    }
    
    .kms-canvas-area {
        min-width: 500px;
    }
}

@media (max-width: 768px) {
    .kms-sidebar {
        width: 350px;
    }
    
    .kms-canvas-area {
        min-width: 400px;
    }
    
    .kms-header {
        padding: 1rem;
    }
    
    .kms-logo {
        font-size: 1.2rem;
    }
    
    /* Responsive Toast Notifications */
    .kms-toast,
    .kms-progress-toast {
        left: 10px;
        right: 10px;
        top: 10px;
        max-width: none;
        min-width: auto;
        width: calc(100% - 20px);
        font-size: 13px;
        padding: 12px 16px;
    }
}

@media (max-width: 480px) {
    .kms-sidebar {
        position: fixed;
        left: -540px;
        top: 70px;
        height: calc(100vh - 70px);
        z-index: 999;
        transition: left 0.3s ease;
        width: 540px;
    }
    
    .kms-sidebar.active {
        left: 0;
    }
    
    .kms-canvas-area {
        width: 100%;
    }
    
    .kms-toast,
    .kms-progress-toast {
        left: 5px;
        right: 5px;
        width: calc(100% - 10px);
        font-size: 12px;
        padding: 10px 14px;
    }
    
    .kms-toast button {
        font-size: 11px;
        padding: 4px 8px;
    }
}

/* Dark Mode Support for Notifications */
@media (prefers-color-scheme: dark) {
    .kms-toast {
        box-shadow: 0 6px 20px rgba(255,255,255,0.1);
    }
    
    .kms-storage-manager-modal > div {
        background: #2c3e50;
        color: var(--text-color-2);
    }
    
    .kms-storage-manager-modal button {
        background: #34495e;
        color: var(--text-color-2);
    }
    
    .kms-storage-manager-modal button:hover {
        background: #3c5a78;
    }
}

/* Accessibility Enhancements */
.kms-toast:focus-within {
    outline: 2px solid var(--color-1);
    outline-offset: 2px;
}

.kms-toast button:focus {
    outline: 2px solid rgba(255,255,255,0.5);
    outline-offset: 1px;
}

/* High Contrast Mode Support */
@media (prefers-contrast: high) {
    .kms-toast {
        border: 3px solid;
        background: none !important;
    }
    
    .kms-toast-success {
        background: #000 !important;
        color: #0f0 !important;
        border-color: #0f0;
    }
    
    .kms-toast-error {
        background: #000 !important;
        color: #f00 !important;
        border-color: #f00;
    }
    
    .kms-toast-warning {
        background: #000 !important;
        color: #ff0 !important;
        border-color: #ff0;
    }
    
    .kms-toast-info {
        background: #000 !important;
        color: #00f !important;
        border-color: #00f;
    }
    
    .kms-toast-storage {
        background: #000 !important;
        color: #0ff !important;
        border-color: #0ff;
    }
}

/* Reduced Motion Support */
@media (prefers-reduced-motion: reduce) {
    .kms-toast,
    .kms-progress-toast,
    .kms-storage-manager-modal,
    .kms-storage-manager-modal > div {
        animation: none;
        transition: none;
    }
    
    .kms-toast:hover,
    .kms-progress-toast:hover {
        transform: none;
        animation: none;
    }
}

/* Enhanced Toast Notification Animations */
@keyframes slideInRight {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

@keyframes slideOutRight {
    from {
        transform: translateX(0);
        opacity: 1;
    }
    to {
        transform: translateX(100%);
        opacity: 0;
    }
}

@keyframes bounceIn {
    0% {
        transform: scale(0.3) translateX(100%);
        opacity: 0;
    }
    50% {
        transform: scale(1.05) translateX(0);
        opacity: 1;
    }
    70% {
        transform: scale(0.9) translateX(0);
    }
    100% {
        transform: scale(1) translateX(0);
        opacity: 1;
    }
}

@keyframes pulse {
    0% {
        box-shadow: 0 6px 20px rgba(0,0,0,0.2);
    }
    50% {
        box-shadow: 0 8px 25px rgba(0,0,0,0.3);
    }
    100% {
        box-shadow: 0 6px 20px rgba(0,0,0,0.2);
    }
}

/* Toast Notification Styles */
.kms-toast {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    backdrop-filter: blur(10px);
    transition: all 0.3s ease;
}

.kms-toast:hover {
    transform: translateY(-2px);
    animation: pulse 2s infinite;
}

.kms-toast-success {
    background: linear-gradient(135deg, var(--color-3), #20c997);
    border-left: 4px solid var(--border-color-3);
}

.kms-toast-error {
    background: linear-gradient(135deg, var(--color-4), #e74c3c);
    border-left: 4px solid var(--border-color-4);
}

.kms-toast-warning {
    background: linear-gradient(135deg, var(--color-2), #f39c12);
    border-left: 4px solid var(--border-color-2);
}

.kms-toast-info {
    background: linear-gradient(135deg, var(--color-1), #3498db);
    border-left: 4px solid var(--border-color-1);
}

.kms-toast-storage {
    background: linear-gradient(135deg, var(--color-5), #17a2b8);
    border-left: 4px solid var(--border-color-5);
}

/* Progress Toast Styles */
.kms-progress-toast {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    backdrop-filter: blur(10px);
    transition: all 0.3s ease;
}

.kms-progress-toast:hover {
    transform: translateY(-2px);
}

/* Storage Manager Modal Enhancements */
.kms-storage-manager-modal {
    backdrop-filter: blur(5px);
    animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

.kms-storage-manager-modal > div {
    animation: slideInUp 0.4s cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

@keyframes slideInUp {
    from {
        transform: translateY(50px);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

/* Enhanced Button Hover Effects */
.kms-storage-manager-modal button:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.2);
    transition: all 0.2s ease;
}

.kms-storage-manager-modal button:active {
    transform: translateY(0);
    box-shadow: 0 2px 6px rgba(0,0,0,0.2);
}

/* Toast Action Button Styles */
.kms-toast button {
    transition: all 0.2s ease;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.kms-toast button:hover {
    transform: scale(1.05);
    box-shadow: 0 2px 8px rgba(0,0,0,0.2);
}

.kms-toast button:active {
    transform: scale(0.98);
}

/* Close Button Enhancement */
.kms-toast button[style*="position: absolute"] {
    border-radius: 50%;
    transition: all 0.2s ease;
}

.kms-toast button[style*="position: absolute"]:hover {
    background: rgba(255,255,255,0.2);
    transform: rotate(90deg) scale(1.1);
}

/* Progress Bar Animation */
@keyframes progressFill {
    from {
        width: 0%;
    }
    to {
        width: var(--progress-width, 0%);
    }
}

/* Responsive Design */
@media (max-width: 1200px) {
    .kms-sidebar {
        width: 400px;
    }
    
    .kms-canvas-area {
        min-width: 500px;
    }
}

@media (max-width: 768px) {
    .kms-sidebar {
        width: 350px;
    }
    
    .kms-canvas-area {
        min-width: 400px;
    }
    
    .kms-header {
        padding: 1rem;
    }
    
    .kms-logo {
        font-size: 1.2rem;
    }
    
    /* Responsive Toast Notifications */
    .kms-toast,
    .kms-progress-toast {
        left: 10px;
        right: 10px;
        top: 10px;
        max-width: none;
        min-width: auto;
        width: calc(100% - 20px);
        font-size: 13px;
        padding: 12px 16px;
    }
}

@media (max-width: 480px) {
    .kms-sidebar {
        position: fixed;
        left: -540px;
        top: 70px;
        height: calc(100vh - 70px);
        z-index: 999;
        transition: left 0.3s ease;
        width: 540px;
    }
    
    .kms-sidebar.active {
        left: 0;
    }
    
    .kms-canvas-area {
        width: 100%;
    }
    
    .kms-toast,
    .kms-progress-toast {
        left: 5px;
        right: 5px;
        width: calc(100% - 10px);
        font-size: 12px;
        padding: 10px 14px;
    }
    
    .kms-toast button {
        font-size: 11px;
        padding: 4px 8px;
    }
}

/* Dark Mode Support for Notifications */
@media (prefers-color-scheme: dark) {
    .kms-toast {
        box-shadow: 0 6px 20px rgba(255,255,255,0.1);
    }
    
    .kms-storage-manager-modal > div {
        background: #2c3e50;
        color: var(--text-color-2);
    }
    
    .kms-storage-manager-modal button {
        background: #34495e;
        color: var(--text-color-2);
    }
    
    .kms-storage-manager-modal button:hover {
        background: #3c5a78;
    }
}

/* Accessibility Enhancements */
.kms-toast:focus-within {
    outline: 2px solid var(--color-1);
    outline-offset: 2px;
}

.kms-toast button:focus {
    outline: 2px solid rgba(255,255,255,0.5);
    outline-offset: 1px;
}

/* High Contrast Mode Support */
@media (prefers-contrast: high) {
    .kms-toast {
        border: 3px solid;
        background: none !important;
    }
    
    .kms-toast-success {
        background: #000 !important;
        color: #0f0 !important;
        border-color: #0f0;
    }
    
    .kms-toast-error {
        background: #000 !important;
        color: #f00 !important;
        border-color: #f00;
    }
    
    .kms-toast-warning {
        background: #000 !important;
        color: #ff0 !important;
        border-color: #ff0;
    }
    
    .kms-toast-info {
        background: #000 !important;
        color: #00f !important;
        border-color: #00f;
    }
    
    .kms-toast-storage {
        background: #000 !important;
        color: #0ff !important;
        border-color: #0ff;
    }
}

/* Reduced Motion Support */
@media (prefers-reduced-motion: reduce) {
    .kms-toast,
    .kms-progress-toast,
    .kms-storage-manager-modal,
    .kms-storage-manager-modal > div {
        animation: none;
        transition: none;
    }
    
    .kms-toast:hover,
    .kms-progress-toast:hover {
        transform: none;
        animation: none;
    }
}

/* Enhanced Toast Notification Animations */
@keyframes slideInRight {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

@keyframes slideOutRight {
    from {
        transform: translateX(0);
        opacity: 1;
    }
    to {
        transform: translateX(100%);
        opacity: 0;
    }
}

@keyframes bounceIn {
    0% {
        transform: scale(0.3) translateX(100%);
        opacity: 0;
    }
    50% {
        transform: scale(1.05) translateX(0);
        opacity: 1;
    }
    70% {
        transform: scale(0.9) translateX(0);
    }
    100% {
        transform: scale(1) translateX(0);
        opacity: 1;
    }
}

@keyframes pulse {
    0% {
        box-shadow: 0 6px 20px rgba(0,0,0,0.2);
    }
    50% {
        box-shadow: 0 8px 25px rgba(0,0,0,0.3);
    }
    100% {
        box-shadow: 0 6px 20px rgba(0,0,0,0.2);
    }
}

/* Toast Notification Styles */
.kms-toast {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    backdrop-filter: blur(10px);
    transition: all 0.3s ease;
}

.kms-toast:hover {
    transform: translateY(-2px);
    animation: pulse 2s infinite;
}

.kms-toast-success {
    background: linear-gradient(135deg, var(--color-3), #20c997);
    border-left: 4px solid var(--border-color-3);
}

.kms-toast-error {
    background: linear-gradient(135deg, var(--color-4), #e74c3c);
    border-left: 4px solid var(--border-color-4);
}

.kms-toast-warning {
    background: linear-gradient(135deg, var(--color-2), #f39c12);
    border-left: 4px solid var(--border-color-2);
}

.kms-toast-info {
    background: linear-gradient(135deg, var(--color-1), #3498db);
    border-left: 4px solid var(--border-color-1);
}

.kms-toast-storage {
    background: linear-gradient(135deg, var(--color-5), #17a2b8);
    border-left: 4px solid var(--border-color-5);
}

/* Progress Toast Styles */
.kms-progress-toast {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    backdrop-filter: blur(10px);
    transition: all 0.3s ease;
}

.kms-progress-toast:hover {
    transform: translateY(-2px);
}

/* Storage Manager Modal Enhancements */
.kms-storage-manager-modal {
    backdrop-filter: blur(5px);
    animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

.kms-storage-manager-modal > div {
    animation: slideInUp 0.4s cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

@keyframes slideInUp {
    from {
        transform: translateY(50px);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

/* Enhanced Button Hover Effects */
.kms-storage-manager-modal button:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.2);
    transition: all 0.2s ease;
}

.kms-storage-manager-modal button:active {
    transform: translateY(0);
    box-shadow: 0 2px 6px rgba(0,0,0,0.2);
}

/* Toast Action Button Styles */
.kms-toast button {
    transition: all 0.2s ease;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.kms-toast button:hover {
    transform: scale(1.05);
    box-shadow: 0 2px 8px rgba(0,0,0,0.2);
}

.kms-toast button:active {
    transform: scale(0.98);
}

/* Close Button Enhancement */
.kms-toast button[style*="position: absolute"] {
    border-radius: 50%;
    transition: all 0.2s ease;
}

.kms-toast button[style*="position: absolute"]:hover {
    background: rgba(255,255,255,0.2);
    transform: rotate(90deg) scale(1.1);
}

/* Progress Bar Animation */
@keyframes progressFill {
    from {
        width: 0%;
    }
    to {
        width: var(--progress-width, 0%);
    }
}

/* Responsive Toast Notifications */
@media (max-width: 768px) {
    .kms-toast,
    .kms-progress-toast {
        left: 10px;
        right: 10px;
        top: 10px;
        max-width: none;
        min-width: auto;
        width: calc(100% - 20px);
    }
    
    .kms-toast {
        font-size: 13px;
        padding: 12px 16px;
    }
    
    .kms-progress-toast {
        font-size: 13px;
        padding: 12px 16px;
    }
}

@media (max-width: 480px) {
    .kms-toast,
    .kms-progress-toast {
        left: 5px;
        right: 5px;
        width: calc(100% - 10px);
        font-size: 12px;
        padding: 10px 14px;
    }
    
    .kms-toast button {
        font-size: 11px;
        padding: 4px 8px;
    }
}

/* Dark Mode Support for Notifications */
@media (prefers-color-scheme: dark) {
    .kms-toast {
        box-shadow: 0 6px 20px rgba(255,255,255,0.1);
    }
    
    .kms-storage-manager-modal > div {
        background: #2c3e50;
        color: var(--text-color-2);
    }
    
    .kms-storage-manager-modal button {
        background: #34495e;
        color: var(--text-color-2);
    }
    
    .kms-storage-manager-modal button:hover {
        background: #3c5a78;
    }
}

/* Accessibility Enhancements */
.kms-toast:focus-within {
    outline: 2px solid var(--color-1);
    outline-offset: 2px;
}

.kms-toast button:focus {
    outline: 2px solid rgba(255,255,255,0.5);
    outline-offset: 1px;
}

/* High Contrast Mode Support */
@media (prefers-contrast: high) {
    .kms-toast {
        border: 3px solid;
        background: none !important;
    }
    
    .kms-toast-success {
        background: #000 !important;
        color: #0f0 !important;
        border-color: #0f0;
    }
    
    .kms-toast-error {
        background: #000 !important;
        color: #f00 !important;
        border-color: #f00;
    }
    
    .kms-toast-warning {
        background: #000 !important;
        color: #ff0 !important;
        border-color: #ff0;
    }
    
    .kms-toast-info {
        background: #000 !important;
        color: #00f !important;
        border-color: #00f;
    }
    
    .kms-toast-storage {
        background: #000 !important;
        color: #0ff !important;
        border-color: #0ff;
    }
}

/* Reduced Motion Support */
@media (prefers-reduced-motion: reduce) {
    .kms-toast,
    .kms-progress-toast,
    .kms-storage-manager-modal,
    .kms-storage-manager-modal > div {
        animation: none;
        transition: none;
    }
    
    .kms-toast:hover,
    .kms-progress-toast:hover {
        transform: none;
        animation: none;
    }
}
