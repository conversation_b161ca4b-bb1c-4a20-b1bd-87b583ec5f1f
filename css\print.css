/* <PERSON><PERSON> Poster Maker - Borderless Print Styles */
/* 無邊框打印樣式 */

:root {
  --color-1: rgb(0, 200, 255);
  --color-2: rgb(255, 200, 0);
  --color-3: rgb(47, 255, 92);
  --color-4: rgb(255, 49, 49);
  --color-5: rgb(0, 255, 234);
  --border-color-1: rgb(0, 162, 255);
  --border-color-2: rgb(255, 180, 19);
  --border-color-3: rgb(30, 255, 0);
  --border-color-4: rgb(255, 63, 63);
  --border-color-5: rgb(12, 255, 243);
  --text-color-1: rgb(0, 0, 0);
  --text-color-2: rgb(255, 255, 255);
  --text-color-3: rgb(0, 255, 128);
  --text-color-4: rgb(255, 128, 0);
  --text-color-5: rgb(255, 0, 128);
}

/* 基礎樣式 - 無邊框打印設置 */
@media print {
    /* 強制所有頁面邊距為零 */
    @page {
        margin: 0 !important;
        padding: 0 !important;
        size: auto !important;
        -webkit-print-color-adjust: exact !important;
        color-adjust: exact !important;
        print-color-adjust: exact !important;
        border: none !important;
        outline: none !important;
    }
    
    /* 針對不同頁面類型的邊距設置 */
    @page :first {
        margin: 0 !important;
        padding: 0 !important;
        border: none !important;
    }
    
    @page :left {
        margin: 0 !important;
        padding: 0 !important;
        border: none !important;
    }
    
    @page :right {
        margin: 0 !important;
        padding: 0 !important;
        border: none !important;
    }
    
    @page :blank {
        margin: 0 !important;
        padding: 0 !important;
        border: none !important;
    }
    
    /* 動態 @page 規則將由 JavaScript 注入，避免硬編碼衝突 */
    
    /* 基礎元素重置 */
    * {
        margin: 0 !important;
        padding: 0 !important;
        -webkit-print-color-adjust: exact !important;
        color-adjust: exact !important;
        print-color-adjust: exact !important;
        box-sizing: border-box !important;
    }
    
    html {
        margin: 0 !important;
        padding: 0 !important;
        background: transparent !important;
        width: 100% !important;
        height: 100% !important;
        overflow: visible !important;
    }
    
    body {
        margin: 0 !important;
        padding: 0 !important;
        background: transparent !important;
        width: 100% !important;
        height: 100% !important;
        overflow: visible !important;
        -webkit-print-color-adjust: exact !important;
        color-adjust: exact !important;
        print-color-adjust: exact !important;
    }
}

/* 布局樣式 - 打印佈局優化 */
@media print {
    /* 隱藏所有非打印元素 */
    body * {
        visibility: hidden !important;
    }
    
    /* 只顯示海報畫布 */
    .kms-poster-canvas,
    .kms-poster-canvas * {
        visibility: visible !important;
        -webkit-print-color-adjust: exact !important;
        color-adjust: exact !important;
        print-color-adjust: exact !important;
    }
    
    /* 海報畫布絕對定位 */
    .kms-poster-canvas {
        position: absolute !important;
        left: 0 !important;
        top: 0 !important;
        width: 100% !important;
        height: 100% !important;
        margin: 0 !important;
        padding: 0 !important;
        border: none !important;
        outline: none !important;
        box-shadow: none !important;
        border-radius: 0 !important;
        transform: none !important;
        page-break-inside: avoid !important;
        overflow: visible !important;
    }
    
    /* 預覽容器設置 */
    .preview-container {
        margin: 0 !important;
        padding: 0 !important;
        width: 100% !important;
        height: 100% !important;
        min-height: auto !important;
        display: block !important;
        position: static !important;
    }
}

/* 交互樣式 - 隱藏控制元素 */
@media print {
    /* 隱藏所有控制元素 */
    .kms-canvas-element.selected::after,
    .kms-resize-handle,
    .kms-canvas-tools,
    .kms-alignment-guide,
    .kms-position-indicator,
    .kms-canvas-grid,
    .print-controls {
        display: none !important;
        visibility: hidden !important;
    }
    
    /* 確保元素正確顯示 */
    .kms-canvas-element {
        position: absolute !important;
        max-width: none !important;
        max-height: none !important;
        overflow: visible !important;
    }
    
    .kms-text-element {
        padding: inherit !important;
        line-height: inherit !important;
        word-wrap: break-word !important;
        white-space: pre-wrap !important;
        background-color: inherit !important;
        color: inherit !important;
        font-family: inherit !important;
        font-size: inherit !important;
        font-weight: inherit !important;
        font-style: inherit !important;
        text-decoration: inherit !important;
        text-align: inherit !important;
        border: inherit !important;
        border-width: inherit !important;
        border-style: inherit !important;
        border-color: inherit !important;
        border-radius: inherit !important;
        text-shadow: inherit !important;
        box-sizing: border-box !important;
        max-width: none !important;
        max-height: none !important;
        transform: none !important;
        zoom: 1 !important;
        scale: 1 !important;
        width: inherit !important;
        height: inherit !important;
        position: absolute !important;
        left: inherit !important;
        top: inherit !important;
        -webkit-print-color-adjust: exact !important;
        color-adjust: exact !important;
        print-color-adjust: exact !important;
    }
    
    .kms-image-element {
        max-width: none !important;
        max-height: none !important;
        overflow: visible !important;
    }
    
    .kms-image-element img {
        width: 100% !important;
        height: 100% !important;
        object-fit: contain !important;
        max-width: none !important;
        max-height: none !important;
    }
    
    .kms-qr-element {
        max-width: none !important;
        max-height: none !important;
        overflow: visible !important;
    }
    
    .kms-qr-element img {
        display: block !important;
        width: 100% !important;
        height: auto !important;
        max-width: none !important;
        max-height: none !important;
    }
}

/* 響應式樣式 - 不同尺寸的打印設置 */
@media print {
    /* Letter 尺寸特定樣式 */
    .kms-canvas-letter {
        width: 8.5in !important;
        height: 11in !important;
        max-width: 8.5in !important;
        max-height: 11in !important;
        position: absolute !important;
        left: 0 !important;
        top: 0 !important;
        margin: 0 !important;
        padding: 0 !important;
        box-sizing: border-box !important;
        transform: none !important;
        scale: 1 !important;
    }
    
    /* 4x6 尺寸特定樣式 */
    .kms-canvas-4x6 {
        width: 4in !important;
        height: 6in !important;
        max-width: none !important;
        max-height: none !important;
        position: absolute !important;
        left: 0 !important;
        top: 0 !important;
    }

    .kms-canvas-4x6-landscape {
        width: 6in !important;
        height: 4in !important;
        max-width: none !important;
        max-height: none !important;
        position: absolute !important;
        left: 0 !important;
        top: 0 !important;
    }
    
    /* Trading Card Layout 打印樣式 */
    .kms-canvas-trading-card-layout {
        width: 8.5in !important;
        height: 11in !important;
        max-width: none !important;
        max-height: none !important;
        position: absolute !important;
        left: 0 !important;
        top: 0 !important;
        background: white !important;
        border: none !important;
    }
    
    /* Individual Trading Card 打印樣式 */
    .kms-trading-card {
        border: 1px solid #000 !important;
        background: white !important;
        border-radius: 4px !important;
        box-shadow: none !important;
        page-break-inside: avoid !important;
        overflow: hidden !important;
    }
    
    .kms-trading-card-label {
        background: #000 !important;
        color: white !important;
        font-size: 8pt !important;
        padding: 2px 4px !important;
    }
    
    .kms-trading-card-content {
        border: 1px dashed #ccc !important;
        font-size: 10pt !important;
        color: #666 !important;
    }
    
    /* Trading Card Grid 打印樣式 */
    .kms-trading-card-grid {
        position: absolute !important;
        left: 0 !important;
        top: 0 !important;
        margin: 0 !important;
        padding: 0 !important;
        background: white !important;
        border: none !important;
        box-shadow: none !important;
        page-break-inside: avoid !important;
    }
    
    .kms-trading-card-grid.letter-layout {
        width: 8.5in !important;
        height: 11in !important;
        display: grid !important;
        grid-template-columns: repeat(3, 2.5in) !important;
        grid-template-rows: repeat(3, 3.5in) !important;
        gap: 0.25in !important;
        padding: 0.25in !important;
    }
    
    .kms-trading-card-grid.photo-layout {
        width: 4in !important;
        height: 6in !important;
        display: grid !important;
        grid-template-columns: repeat(1, 3.5in) !important;
        grid-template-rows: repeat(2, 2.5in) !important;
        gap: 0.25in !important;
        padding: 0.25in !important;
        justify-content: center !important;
    }
    
    .kms-trading-card-item {
        width: 2.5in !important;
        height: 3.5in !important;
        border: 1px solid #000 !important;
        background: white !important;
        page-break-inside: avoid !important;
        overflow: hidden !important;
        position: relative !important;
    }
    
    /* 4x6 佈局中的橫向卡片 */
    .kms-trading-card-grid.photo-layout .kms-trading-card-item {
        width: 3.5in !important;
        height: 2.5in !important;
    }
    
    .kms-trading-card-content {
        width: 100% !important;
        height: 100% !important;
        padding: 0.1in !important;
        display: flex !important;
        flex-direction: column !important;
        justify-content: center !important;
        align-items: center !important;
    }
    
    .kms-trading-card-number {
        display: none !important;
    }
}

/* 瀏覽器特定的無邊框打印修正 */
@media print {
    /* 強制性的全局打印設定 */
    * {
        -webkit-print-color-adjust: exact !important;
        color-adjust: exact !important;
        print-color-adjust: exact !important;
    }
    
    html {
        margin: 0 !important;
        padding: 0 !important;
        width: 100% !important;
        height: 100% !important;
        overflow: visible !important;
    }
    
    body {
        margin: 0 !important;
        padding: 0 !important;
        width: 100% !important;
        height: 100% !important;
        overflow: visible !important;
        -webkit-print-color-adjust: exact !important;
        color-adjust: exact !important;
        print-color-adjust: exact !important;
        transform: none !important;
        zoom: 1 !important;
    }
    
    /* 強制性的畫布設定 */
    .kms-poster-canvas {
        position: absolute !important;
        left: 0 !important;
        top: 0 !important;
        margin: 0 !important;
        padding: 0 !important;
        border: none !important;
        box-shadow: none !important;
        transform: none !important;
        transform-origin: top left !important;
        zoom: 1 !important;
        scale: 1 !important;
        overflow: visible !important;
    }
    
    /* Letter 尺寸強制設定 */
    .kms-canvas-letter {
        width: 8.5in !important;
        height: 11in !important;
        min-width: 8.5in !important;
        min-height: 11in !important;
        max-width: 8.5in !important;
        max-height: 11in !important;
    }
    
    /* Chrome/Safari 特定修正 */
    @supports (-webkit-appearance: none) {
        @page {
            margin: 0 !important;
            padding: 0 !important;
            size: letter !important;
            -webkit-print-color-adjust: exact !important;
        }
        
        .kms-poster-canvas {
            -webkit-transform: none !important;
            -webkit-transform-origin: top left !important;
        }
    }
    
    /* Firefox 特定修正 */
    @supports (-moz-appearance: none) {
        @page {
            margin: 0 !important;
            padding: 0 !important;
            size: letter !important;
        }
        
        .kms-poster-canvas {
            -moz-transform: none !important;
            -moz-transform-origin: top left !important;
        }
    }
    
    /* Edge 特定修正 */
    @supports (-ms-ime-align: auto) {
        @page {
            margin: 0 !important;
            padding: 0 !important;
            size: letter !important;
        }
        
        .kms-poster-canvas {
            -ms-transform: none !important;
            -ms-transform-origin: top left !important;
        }
    }
    
    /* 通用強制性規則 - 覆蓋所有可能的衝突 */
    @page {
        margin: 0 !important;
        padding: 0 !important;
        size: letter !important;
        -webkit-print-color-adjust: exact !important;
        color-adjust: exact !important;
        print-color-adjust: exact !important;
    }
    
    /* 強制重置所有元素 */
    * {
        -webkit-print-color-adjust: exact !important;
        color-adjust: exact !important;
        print-color-adjust: exact !important;
        box-sizing: border-box !important;
    }
    
    /* 強制性的根元素設定 */
    html {
        margin: 0 !important;
        padding: 0 !important;
        width: 100% !important;
        height: 100% !important;
        overflow: visible !important;
        transform: none !important;
        zoom: 1 !important;
    }
    
    body {
        margin: 0 !important;
        padding: 0 !important;
        width: 100% !important;
        height: 100% !important;
        overflow: visible !important;
        transform: none !important;
        zoom: 1 !important;
    }
    
    /* 最高優先級的畫布設定 */
    .kms-poster-canvas,
    #posterCanvas {
        position: absolute !important;
        left: 0 !important;
        top: 0 !important;
        margin: 0 !important;
        padding: 0 !important;
        border: none !important;
        box-shadow: none !important;
        transform: none !important;
        transform-origin: top left !important;
        zoom: 1 !important;
        scale: 1 !important;
        overflow: visible !important;
        width: 8.5in !important;
        height: 11in !important;
        min-width: 8.5in !important;
        min-height: 11in !important;
        max-width: 8.5in !important;
        max-height: 11in !important;
    }
}

/* 強制無邊框設置 - 最高優先級 */
@media print {
    /* 確保沒有任何邊距或填充 */
    * {
        margin: 0 !important;
        padding: 0 !important;
    }
    
    /* 移除所有可能的邊框和陰影 */
    .kms-poster-canvas {
        box-shadow: none !important;
        border: none !important;
        outline: none !important;
        margin: 0 !important;
        padding: 0 !important;
    }
}