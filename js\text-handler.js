/**
 * KMS Poster Maker - Text Handler
 * 文字處理模塊
 */

class KMSTextHandler {
    constructor(posterMaker) {
        this.posterMaker = posterMaker;
        this.init();
    }
    
    init() {
        this.setupTextControls();
        this.initializeRotationButton();
    }
    
    setupTextControls() {
        // Font family control
        const fontFamily = document.getElementById('fontFamily');
        if (fontFamily) {
            fontFamily.addEventListener('change', (e) => {
                this.updateSelectedTextStyle('fontFamily', e.target.value);
            });
        }
        
        // Font size control
        const fontSize = document.getElementById('fontSize');
        const fontSizeValue = document.getElementById('fontSizeValue');
        if (fontSize && fontSizeValue) {
            fontSize.addEventListener('input', (e) => {
                const size = e.target.value + 'px';
                fontSizeValue.textContent = size;
                this.updateSelectedTextStyle('fontSize', size);
            });
        }
        
        // Font color control
        const fontColor = document.getElementById('fontColor');
        if (fontColor) {
            fontColor.addEventListener('change', (e) => {
                this.updateSelectedTextStyle('color', e.target.value);
                this.updateColorPreview(fontColor, e.target.value);
            });
        }
        
        // Text style buttons (bold, italic, underline)
        const styleButtons = document.querySelectorAll('.kms-enhanced-style-btn[data-style]');
        styleButtons.forEach(btn => {
            btn.addEventListener('click', (e) => {
                e.preventDefault();
                const style = btn.dataset.style;
                this.toggleTextStyle(btn, style);
            });
        });
        
        // Border controls
        const borderWidth = document.getElementById('borderWidth');
        const borderWidthValue = document.getElementById('borderWidthValue');
        if (borderWidth && borderWidthValue) {
            borderWidth.addEventListener('input', (e) => {
                const value = e.target.value + 'px';
                borderWidthValue.textContent = value;
                this.updateSelectedTextBorder('borderWidth', value);
            });
        }
        
        const borderRadius = document.getElementById('borderRadius');
        const borderRadiusValue = document.getElementById('borderRadiusValue');
        if (borderRadius && borderRadiusValue) {
            borderRadius.addEventListener('input', (e) => {
                const value = e.target.value + 'px';
                borderRadiusValue.textContent = value;
                this.updateSelectedTextBorder('borderRadius', value);
            });
        }
        
        const borderColor = document.getElementById('borderColor');
        if (borderColor) {
            borderColor.addEventListener('change', (e) => {
                this.updateSelectedTextBorder('borderColor', e.target.value);
                this.updateColorPreview(borderColor, e.target.value);
            });
        }

        // Text background color
        const textBgColor = document.getElementById('textBgColor');
        if (textBgColor) {
            textBgColor.addEventListener('change', (e) => {
                this.updateSelectedTextBackground();
                this.updateColorPreview(textBgColor, e.target.value);
            });
        }

        // Text background opacity
        const textBgOpacity = document.getElementById('textBgOpacity');
        const textBgOpacityValue = document.getElementById('textBgOpacityValue');
        if (textBgOpacity && textBgOpacityValue) {
            textBgOpacity.addEventListener('input', (e) => {
                const opacity = e.target.value;
                textBgOpacityValue.textContent = opacity + '%';
                this.updateSelectedTextBackground();
            });
        }

        // Text shadow controls
        const shadowBlur = document.getElementById('shadowBlur');
        const shadowBlurValue = document.getElementById('shadowBlurValue');
        if (shadowBlur && shadowBlurValue) {
            shadowBlur.addEventListener('input', (e) => {
                shadowBlurValue.textContent = e.target.value + 'px';
                this.updateTextShadow();
            });
        }

        const shadowDistance = document.getElementById('shadowDistance');
        const shadowDistanceValue = document.getElementById('shadowDistanceValue');
        if (shadowDistance && shadowDistanceValue) {
            shadowDistance.addEventListener('input', (e) => {
                shadowDistanceValue.textContent = e.target.value + 'px';
                this.updateTextShadow();
            });
        }

        const shadowColor = document.getElementById('shadowColor');
        if (shadowColor) {
            shadowColor.addEventListener('change', (e) => {
                this.updateTextShadow();
                this.updateColorPreview(shadowColor, e.target.value);
            });
        }

        // Text alignment controls
        const alignButtons = document.querySelectorAll('[data-align]');
        alignButtons.forEach(btn => {
            btn.addEventListener('click', (e) => {
                e.preventDefault();
                const align = btn.dataset.align;
                this.updateTextAlignment(align);
                this.updateAlignmentButtons(align);
            });
        });

        // Text size controls
        const textWidth = document.getElementById('textWidth');
        const textWidthValue = document.getElementById('textWidthValue');
        if (textWidth && textWidthValue) {
            textWidth.addEventListener('input', (e) => {
                const value = e.target.value + 'px';
                textWidthValue.textContent = value;
                this.updateSelectedTextStyle('width', value);
            });
        }

        const textHeight = document.getElementById('textHeight');
        const textHeightValue = document.getElementById('textHeightValue');
        if (textHeight && textHeightValue) {
            textHeight.addEventListener('input', (e) => {
                const value = e.target.value + 'px';
                textHeightValue.textContent = value;
                this.updateSelectedTextStyle('height', value);
            });
        }

        // Background opacity (handled above in the combined text background section)
    }
    
    updateSelectedTextStyle(property, value) {
        const selectedElement = this.posterMaker.selectedElement;
        if (!selectedElement || !selectedElement.classList.contains('kms-text-element')) {
            return;
        }
        
        selectedElement.style[property] = value;
        
        // 觸發數據保存
        if (this.posterMaker.posterData) {
            this.posterMaker.posterData.saveCurrentState();
        }
    }
    
    updateSelectedTextBorder(property, value) {
        const selectedElement = this.posterMaker.selectedElement;
        if (!selectedElement || !selectedElement.classList.contains('kms-text-element')) {
            return;
        }
        
        switch (property) {
            case 'borderWidth':
                selectedElement.style.borderWidth = value;
                selectedElement.style.borderStyle = value === '0px' ? 'none' : 'solid';
                break;
            case 'borderColor':
                selectedElement.style.borderColor = value;
                break;
            case 'borderRadius':
                selectedElement.style.borderRadius = value;
                break;
        }
        
        // 觸發數據保存
        if (this.posterMaker.posterData) {
            this.posterMaker.posterData.saveCurrentState();
        }
    }
    
    toggleTextStyle(button, style) {
        const selectedElement = this.posterMaker.selectedElement;
        if (!selectedElement || !selectedElement.classList.contains('kms-text-element')) {
            return;
        }
        
        const isActive = button.classList.contains('active');
        
        switch (style) {
            case 'bold':
                selectedElement.style.fontWeight = isActive ? 'normal' : 'bold';
                break;
            case 'italic':
                selectedElement.style.fontStyle = isActive ? 'normal' : 'italic';
                break;
            case 'underline':
                selectedElement.style.textDecoration = isActive ? 'none' : 'underline';
                break;
        }
        
        button.classList.toggle('active');
        
        // 觸發數據保存
        if (this.posterMaker.posterData) {
            this.posterMaker.posterData.saveCurrentState();
        }
    }
    
    // Trading card support methods
    checkAndApplyToTradingCards(textElement) {
        // Check if trading card mode is active
        const tradingCardSection = document.getElementById('tradingCardLayoutSection');
        if (tradingCardSection && !tradingCardSection.classList.contains('hidden') && window.kmsTradingCard) {
            // Auto-apply text to all trading cards when created
            setTimeout(() => {
                this.applyTextToTradingCards(textElement);
            }, 100);
        }
    }
    
    applyTextToTradingCards(textElement) {
        if (!window.kmsTradingCard || !textElement) return;
        
        const text = textElement.textContent;
        const styles = {
            fontSize: textElement.style.fontSize || '16px',
            fontFamily: textElement.style.fontFamily || 'Arial, sans-serif',
            color: textElement.style.color || '#333333',
            fontWeight: textElement.style.fontWeight || 'normal',
            fontStyle: textElement.style.fontStyle || 'normal',
            textDecoration: textElement.style.textDecoration || 'none',
            textAlign: textElement.style.textAlign || 'center',
            backgroundColor: textElement.style.backgroundColor || 'transparent',
            borderWidth: textElement.style.borderWidth || '0px',
            borderColor: textElement.style.borderColor || 'transparent',
            borderRadius: textElement.style.borderRadius || '0px',
            textShadow: textElement.style.textShadow || 'none'
        };
        
        window.kmsTradingCard.applyTextToAllCards(text, styles);
    }
    
    // Method to manually apply current canvas text to trading cards
    applyCanvasTextToTradingCards() {
        if (!window.kmsTradingCard) return;
        
        const canvas = document.getElementById('posterCanvas');
        const textElements = canvas.querySelectorAll('.kms-text-element');
        
        if (textElements.length === 0) {
            window.kmsTradingCard.showMessage('請先在畫布上添加文字');
            return;
        }
        
        // Apply the first text element or selected text element
        const selectedElement = this.posterMaker.selectedElement;
        const targetElement = (selectedElement && selectedElement.classList.contains('kms-text-element')) 
            ? selectedElement 
            : textElements[0];
            
        this.applyTextToTradingCards(targetElement);
    }
    
    updateColorPreview(input, color) {
        const preview = input.nextElementSibling;
        if (preview && preview.classList.contains('kms-color-preview')) {
            preview.style.backgroundColor = color;
            preview.dataset.color = color;
        }
    }

    updateTextShadow() {
        const selectedElement = this.posterMaker.selectedElement;
        if (!selectedElement || !selectedElement.classList.contains('kms-text-element')) {
            return;
        }

        const shadowBlur = document.getElementById('shadowBlur');
        const shadowDistance = document.getElementById('shadowDistance');
        const shadowColor = document.getElementById('shadowColor');

        if (shadowBlur && shadowDistance && shadowColor) {
            const blur = shadowBlur.value;
            const distance = shadowDistance.value;
            const color = shadowColor.value;

            if (blur === '0' && distance === '0') {
                selectedElement.style.textShadow = 'none';
            } else {
                selectedElement.style.textShadow = `${distance}px ${distance}px ${blur}px ${color}`;
            }
        }
        
        // 觸發數據保存
        if (this.posterMaker.posterData) {
            this.posterMaker.posterData.saveCurrentState();
        }
    }

    updateTextAlignment(align) {
        const selectedElement = this.posterMaker.selectedElement;
        if (!selectedElement || !selectedElement.classList.contains('kms-text-element')) {
            return;
        }

        selectedElement.style.textAlign = align;
        
        // 觸發數據保存
        if (this.posterMaker.posterData) {
            this.posterMaker.posterData.saveCurrentState();
        }
    }

    updateAlignmentButtons(activeAlign) {
        const alignButtons = document.querySelectorAll('[data-align]');
        alignButtons.forEach(btn => {
            btn.classList.toggle('active', btn.dataset.align === activeAlign);
        });
    }

    updateSelectedTextBackground() {
        const selectedElement = this.posterMaker.selectedElement;
        if (!selectedElement || !selectedElement.classList.contains('kms-text-element')) {
            return;
        }

        const textBgColor = document.getElementById('textBgColor');
        const textBgOpacity = document.getElementById('textBgOpacity');
        
        if (textBgColor && textBgOpacity) {
            const bgColor = textBgColor.value;
            const opacity = textBgOpacity.value / 100;
            const rgb = this.hexToRgb(bgColor);
            
            if (rgb) {
                selectedElement.style.backgroundColor = `rgba(${rgb.r}, ${rgb.g}, ${rgb.b}, ${opacity})`;
            }
        }
        
        // 觸發數據保存
        if (this.posterMaker.posterData) {
            this.posterMaker.posterData.saveCurrentState();
        }
    }

    updateTextBackgroundOpacity(opacity) {
        // This method is kept for backward compatibility
        this.updateSelectedTextBackground();
    }

    hexToRgb(hex) {
        const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
        return result ? {
            r: parseInt(result[1], 16),
            g: parseInt(result[2], 16),
            b: parseInt(result[3], 16)
        } : null;
    }

    initializeRotationButton() {
        const rotateBtn = document.getElementById('rotateTextBtn');
        if (rotateBtn) {
            rotateBtn.addEventListener('click', () => this.rotateSelectedElement());
        }
    }

    rotateSelectedElement() {
        console.log('rotateSelectedElement called');
        
        if (!this.posterMaker.selectedElement) {
            console.warn('No element selected for rotation');
            return;
        }

        const element = this.posterMaker.selectedElement;
        console.log('Rotating element:', element);

        // 檢查元素是否為文字元素
        if (!element.classList.contains('kms-text-element') && 
            !element.classList.contains('kms-trading-card-text-element')) {
            console.warn('Selected element is not a text element');
            return;
        }

        // 獲取當前旋轉角度
        let currentRotation = 0;
        const transform = element.style.transform || '';
        const rotateMatch = transform.match(/rotate\((-?\d+)deg\)/);
        if (rotateMatch) {
            currentRotation = parseInt(rotateMatch[1]);
        }

        // 增加 90 度
        const newRotation = (currentRotation + 90) % 360;

        // 應用新的旋轉
        const otherTransforms = transform.replace(/rotate\(-?\d+deg\)\s*/g, '').trim();
        const newTransform = `${otherTransforms} rotate(${newRotation}deg)`.trim();
        element.style.transform = newTransform;

        console.log('Applied rotation:', newTransform);

        if (this.posterMaker && this.posterMaker.showMessage) {
            this.posterMaker.showMessage(`文字已旋轉到 ${newRotation}°`);
        }
    }
    
    // Update controls when a text element is selected
    updateControlsForTextElement(element) {
        const computedStyle = window.getComputedStyle(element);
        
        // Update font family
        const fontFamily = document.getElementById('fontFamily');
        if (fontFamily) {
            // Try to match the current font with available options
            const currentFont = computedStyle.fontFamily.toLowerCase();
            const options = fontFamily.options;

            for (let i = 0; i < options.length; i++) {
                const optionFont = options[i].value.toLowerCase();
                if (currentFont.includes(optionFont.split(',')[0].trim())) {
                    fontFamily.selectedIndex = i;
                    break;
                }
            }
        }
        
        // Update font size
        const fontSize = document.getElementById('fontSize');
        const fontSizeValue = document.getElementById('fontSizeValue');
        if (fontSize && fontSizeValue) {
            const currentSize = parseInt(computedStyle.fontSize);
            fontSize.value = currentSize;
            fontSizeValue.textContent = currentSize + 'px';
        }
        
        // Update font color
        const fontColor = document.getElementById('fontColor');
        if (fontColor) {
            const currentColor = this.rgbToHex(computedStyle.color);
            fontColor.value = currentColor;
            this.updateColorPreview(fontColor, currentColor);
        }
        
        // Update style buttons
        this.updateStyleButtons(computedStyle);
        
        // Update border controls
        this.updateBorderControls(computedStyle);
        
        // Update shadow controls
        this.updateShadowControls(computedStyle);
    }
    
    updateStyleButtons(computedStyle) {
        const boldBtn = document.getElementById('boldBtn');
        const italicBtn = document.getElementById('italicBtn');
        const underlineBtn = document.getElementById('underlineBtn');
        
        if (boldBtn) {
            boldBtn.classList.toggle('active', 
                computedStyle.fontWeight === 'bold' || parseInt(computedStyle.fontWeight) >= 600);
        }
        
        if (italicBtn) {
            italicBtn.classList.toggle('active', computedStyle.fontStyle === 'italic');
        }
        
        if (underlineBtn) {
            underlineBtn.classList.toggle('active', 
                computedStyle.textDecoration.includes('underline'));
        }
    }
    
    updateBorderControls(computedStyle) {
        const borderWidth = document.getElementById('borderWidth');
        const borderRadius = document.getElementById('borderRadius');
        const borderColor = document.getElementById('borderColor');
        
        if (borderWidth) {
            const currentWidth = parseInt(computedStyle.borderWidth) || 0;
            borderWidth.value = currentWidth;
        }
        
        if (borderRadius) {
            const currentRadius = parseInt(computedStyle.borderRadius) || 0;
            borderRadius.value = currentRadius;
        }
        
        if (borderColor) {
            const currentColor = this.rgbToHex(computedStyle.borderColor);
            borderColor.value = currentColor;
            this.updateColorPreview(borderColor, currentColor);
        }
    }
    
    updateShadowControls(computedStyle) {
        const shadowBlur = document.getElementById('shadowBlur');
        const shadowDistance = document.getElementById('shadowDistance');
        const shadowColor = document.getElementById('shadowColor');
        const shadowBlurValue = document.getElementById('shadowBlurValue');
        const shadowDistanceValue = document.getElementById('shadowDistanceValue');
        
        // Parse text shadow value
        const textShadow = computedStyle.textShadow;
        
        if (textShadow && textShadow !== 'none') {
            // Parse shadow values (format: "2px 2px 4px rgb(0, 0, 0)")
            const shadowMatch = textShadow.match(/([+-]?\d*\.?\d+)px\s+([+-]?\d*\.?\d+)px\s+([+-]?\d*\.?\d+)px\s+(.+)/);
            
            if (shadowMatch) {
                const distance = Math.abs(parseFloat(shadowMatch[1]));
                const blur = parseFloat(shadowMatch[3]);
                const color = shadowMatch[4];
                
                if (shadowDistance) {
                    shadowDistance.value = distance;
                }
                if (shadowDistanceValue) {
                    shadowDistanceValue.textContent = distance + 'px';
                }
                
                if (shadowBlur) {
                    shadowBlur.value = blur;
                }
                if (shadowBlurValue) {
                    shadowBlurValue.textContent = blur + 'px';
                }
                
                if (shadowColor) {
                    const hexColor = this.rgbToHex(color);
                    shadowColor.value = hexColor;
                    this.updateColorPreview(shadowColor, hexColor);
                }
            }
        } else {
            // No shadow - reset controls
            if (shadowDistance) {
                shadowDistance.value = 0;
            }
            if (shadowDistanceValue) {
                shadowDistanceValue.textContent = '0px';
            }
            
            if (shadowBlur) {
                shadowBlur.value = 0;
            }
            if (shadowBlurValue) {
                shadowBlurValue.textContent = '0px';
            }
            
            if (shadowColor) {
                shadowColor.value = '#000000';
                this.updateColorPreview(shadowColor, '#000000');
            }
        }
    }
    
    // Utility function to convert RGB to HEX
    rgbToHex(rgb) {
        if (rgb.startsWith('#')) return rgb;
        
        const result = rgb.match(/\d+/g);
        if (!result || result.length < 3) return '#000000';
        
        const r = parseInt(result[0]);
        const g = parseInt(result[1]);
        const b = parseInt(result[2]);
        
        return '#' + ((1 << 24) + (r << 16) + (g << 8) + b).toString(16).slice(1);
    }
    
    // Create a new text element with default styling
    createTextElement(text = null) {
        // 獲取應該添加元素的容器
        const container = window.kmsTradingCard?.getElementContainer() || document.getElementById('posterCanvas');
        if (!container) {
            if (this.posterMaker && this.posterMaker.showMessage) {
                this.posterMaker.showMessage('無法添加文字：請先選擇編輯模式或確保畫布可用');
            }
            return null;
        }
        
        const textElement = document.createElement('div');

        // 根據容器類型設置不同的類名
        const isInTradingCard = container.classList.contains('kms-trading-card-content');
        if (isInTradingCard) {
            textElement.className = 'kms-trading-card-text-element';
        } else {
            textElement.className = 'kms-canvas-element kms-text-element';
        }

        textElement.contentEditable = true;
        textElement.textContent = text || (this.posterMaker.currentLanguage === 'en' ? 'Double click to edit' : '雙擊編輯文字');
        
        // Default styling
        textElement.style.position = 'absolute';
        textElement.style.left = '50px';
        textElement.style.top = '50px';
        textElement.style.fontSize = '24px';
        textElement.style.fontFamily = 'Arial, sans-serif';
        textElement.style.color = '#333333';
        textElement.style.padding = '8px';
        textElement.style.minWidth = '50px';
        textElement.style.minHeight = '30px';
        textElement.style.outline = 'none';
        textElement.style.border = 'none';
        textElement.style.backgroundColor = 'transparent';
        textElement.style.lineHeight = '1.4';
        textElement.style.wordWrap = 'break-word';
        textElement.style.whiteSpace = 'pre-wrap';
        textElement.style.textShadow = 'none';
        textElement.style.cursor = 'move';
        textElement.style.userSelect = 'none';
        
        textElement.dataset.elementId = `text_${++this.posterMaker.elementCounter}`;
        
        // Setup text editing
        this.setupTextEditing(textElement);
        
        // 直接添加到容器中
        container.appendChild(textElement);

        // Setup element interaction (drag and select)
        this.posterMaker.setupElementInteraction(textElement);
        
        // 額外添加點擊事件以確保文字元素能被選中
        textElement.addEventListener('click', (e) => {
            // 如果不是雙擊編輯模式，就選中元素
            if (e.detail !== 2) {
                e.stopPropagation();
                this.posterMaker.selectElement(textElement);
            }
        });

        // Add resize handles
        this.addResizeHandles(textElement);

        // 添加到 elements 數組中以支持 Layer List
        if (!this.posterMaker.elements.includes(textElement)) {
            this.posterMaker.elements.push(textElement);
        }

        // 更新 Layer Manager
        if (this.posterMaker.layerManager) {
            this.posterMaker.layerManager.onElementAdded(textElement);
        }

        // 如果是在 trading card 模式且是同步模式，觸發同步
        if (isInTradingCard && window.kmsTradingCard?.editMode === 'sync') {
            // 延遲觸發同步，讓元素先完全添加
            setTimeout(() => {
                window.kmsTradingCard.syncCard1ToAll();
            }, 100);
        }

        // 自動選中新創建的文字元素以顯示控制面板
        setTimeout(() => {
            this.posterMaker.selectElement(textElement);
        }, 50);

        return textElement;
    }
    
    // Handle text element editing
    setupTextEditing(element) {
        let isEditing = false;
        
        // Double-click to edit
        element.addEventListener('dblclick', (e) => {
            e.stopPropagation();
            e.preventDefault();
            isEditing = true;
            element.style.cursor = 'text';
            element.style.userSelect = 'text';
            element.contentEditable = true;
            element.focus();
            
            // Select all text for easy editing
            setTimeout(() => {
                const range = document.createRange();
                range.selectNodeContents(element);
                const selection = window.getSelection();
                selection.removeAllRanges();
                selection.addRange(range);
            }, 10);
        });

        element.addEventListener('blur', () => {
            // Exit editing mode
            isEditing = false;
            element.style.cursor = 'move';
            element.style.userSelect = 'none';
            element.contentEditable = false;
            
            // Save changes when element loses focus
            if (element.textContent.trim() === '') {
                element.textContent = this.posterMaker.currentLanguage === 'en' ? 'Double click to edit' : '雙擊編輯文字';
            }
            
            // Ensure resize handles exist after editing
            const existingHandles = element.querySelectorAll('.kms-resize-handle');
            if (existingHandles.length === 0) {
                this.addResizeHandles(element);
            }
            
            // Re-select the element to ensure it's properly selected
            if (this.posterMaker && this.posterMaker.selectElement) {
                this.posterMaker.selectElement(element);
            }
        });

        element.addEventListener('keydown', (e) => {
            if (isEditing) {
                // Prevent dragging while editing
                e.stopPropagation();

                // Handle Enter key
                if (e.key === 'Enter' && !e.shiftKey) {
                    e.preventDefault();
                    element.blur();
                }
                
                // Handle Escape key
                if (e.key === 'Escape') {
                    e.preventDefault();
                    element.blur();
                }
            }
        });

        element.addEventListener('input', () => {
            if (isEditing) {
                // Auto-resize element based on content
                this.autoResizeTextElement(element);
            }
        });

        // Prevent dragging when in edit mode
        element.addEventListener('mousedown', (e) => {
            if (isEditing || element === document.activeElement) {
                e.stopPropagation();
                return false;
            }
        });
        
        // Initially set to non-editable
        element.contentEditable = false;
    }
    
    autoResizeTextElement(element) {
        // Create a temporary element to measure text size
        const temp = document.createElement('div');
        temp.style.position = 'absolute';
        temp.style.visibility = 'hidden';
        temp.style.whiteSpace = 'pre-wrap';
        temp.style.wordWrap = 'break-word';
        temp.style.font = window.getComputedStyle(element).font;
        temp.style.padding = window.getComputedStyle(element).padding;
        temp.textContent = element.textContent;
        
        document.body.appendChild(temp);
        
        const rect = temp.getBoundingClientRect();
        element.style.width = Math.max(50, rect.width) + 'px';
        element.style.height = Math.max(30, rect.height) + 'px';
        
        document.body.removeChild(temp);
    }
    
    addResizeHandles(element) {
        const handles = ['nw', 'ne', 'sw', 'se', 'n', 's', 'w', 'e'];
        
        handles.forEach(direction => {
            const handle = document.createElement('div');
            handle.className = `kms-resize-handle ${direction}`;
            handle.addEventListener('mousedown', (e) => {
                e.stopPropagation();
                this.startResize(e, element, direction);
            });
            element.appendChild(handle);
        });
    }
    
    startResize(e, element, direction) {
        e.preventDefault();
        
        const startX = e.clientX;
        const startY = e.clientY;
        const startWidth = parseInt(window.getComputedStyle(element).width);
        const startHeight = parseInt(window.getComputedStyle(element).height);
        const startLeft = parseInt(element.style.left);
        const startTop = parseInt(element.style.top);
        
        const canvas = document.getElementById('posterCanvas');
        const canvasRect = canvas.getBoundingClientRect();
        
        const handleResize = (e) => {
            const deltaX = e.clientX - startX;
            const deltaY = e.clientY - startY;
            
            let newWidth = startWidth;
            let newHeight = startHeight;
            let newLeft = startLeft;
            let newTop = startTop;
            
            // Calculate new dimensions based on direction
            switch (direction) {
                case 'se':
                    newWidth = Math.max(50, startWidth + deltaX);
                    newHeight = Math.max(30, startHeight + deltaY);
                    break;
                case 'sw':
                    newWidth = Math.max(50, startWidth - deltaX);
                    newHeight = Math.max(30, startHeight + deltaY);
                    newLeft = startLeft + deltaX;
                    break;
                case 'ne':
                    newWidth = Math.max(50, startWidth + deltaX);
                    newHeight = Math.max(30, startHeight - deltaY);
                    newTop = startTop + deltaY;
                    break;
                case 'nw':
                    newWidth = Math.max(50, startWidth - deltaX);
                    newHeight = Math.max(30, startHeight - deltaY);
                    newLeft = startLeft + deltaX;
                    newTop = startTop + deltaY;
                    break;
                case 'e':
                    newWidth = Math.max(50, startWidth + deltaX);
                    break;
                case 'w':
                    newWidth = Math.max(50, startWidth - deltaX);
                    newLeft = startLeft + deltaX;
                    break;
                case 'n':
                    newHeight = Math.max(30, startHeight - deltaY);
                    newTop = startTop + deltaY;
                    break;
                case 's':
                    newHeight = Math.max(30, startHeight + deltaY);
                    break;
            }
            
            // Apply constraints to keep element within canvas
            if (newLeft < 0) {
                newWidth += newLeft;
                newLeft = 0;
            }
            if (newTop < 0) {
                newHeight += newTop;
                newTop = 0;
            }
            if (newLeft + newWidth > canvas.offsetWidth) {
                newWidth = canvas.offsetWidth - newLeft;
            }
            if (newTop + newHeight > canvas.offsetHeight) {
                newHeight = canvas.offsetHeight - newTop;
            }
            
            // Apply new dimensions
            element.style.width = newWidth + 'px';
            element.style.height = newHeight + 'px';
            element.style.left = newLeft + 'px';
            element.style.top = newTop + 'px';
        };
        
        const stopResize = () => {
            document.removeEventListener('mousemove', handleResize);
            document.removeEventListener('mouseup', stopResize);
        };
        
        document.addEventListener('mousemove', handleResize);
        document.addEventListener('mouseup', stopResize);
    }
}

// Initialize text handler when main app is ready
// TextHandler is now initialized in main.js
