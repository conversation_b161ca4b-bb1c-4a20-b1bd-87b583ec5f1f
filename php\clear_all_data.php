<?php
/**
 * K<PERSON> Poster Maker - Clear All Data API
 * 清除所有資料 API
 */

require_once 'config.php';

// 處理 CORS 預檢請求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    header('Access-Control-Allow-Origin: *');
    header('Access-Control-Allow-Methods: POST, OPTIONS');
    header('Access-Control-Allow-Headers: Content-Type');
    exit;
}

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    sendError('不允許的請求方法', 405);
}

try {
    // 獲取資料庫連接
    $db = Database::getInstance()->getConnection();
    
    // 開始事務
    $db->beginTransaction();
    
    // 獲取所有未保存的圖片記錄（不屬於任何已保存海報的圖片）
    $stmt = $db->prepare("
        SELECT pi.server_filename, pi.file_path 
        FROM poster_images pi 
        LEFT JOIN posters p ON pi.poster_id = p.id 
        WHERE p.id IS NULL OR pi.poster_id IS NULL
    ");
    $stmt->execute();
    $orphanImages = $stmt->fetchAll();
    
    // 刪除孤立的圖片檔案
    foreach ($orphanImages as $image) {
        if (!empty($image['file_path']) && file_exists($image['file_path'])) {
            unlink($image['file_path']);
        }
        // 也嘗試從uploads/images目錄刪除
        if (!empty($image['server_filename'])) {
            $imagePath = IMAGE_UPLOAD_PATH . $image['server_filename'];
            if (file_exists($imagePath)) {
                unlink($imagePath);
            }
        }
    }
    
    // 刪除資料庫中的孤立圖片記錄
    $stmt = $db->prepare("
        DELETE pi FROM poster_images pi 
        LEFT JOIN posters p ON pi.poster_id = p.id 
        WHERE p.id IS NULL OR pi.poster_id IS NULL
    ");
    $stmt->execute();
    
    // 提交事務
    $db->commit();
    
    sendResponse([
        'deleted_images' => count($orphanImages),
        'message' => '已清除 ' . count($orphanImages) . ' 個未保存的圖片'
    ], 200, '資料清除成功');
    
} catch (Exception $e) {
    // 回滾事務
    if ($db && $db->inTransaction()) {
        $db->rollBack();
    }
    
    error_log('Clear data error: ' . $e->getMessage());
    sendError('清除資料時發生錯誤: ' . $e->getMessage(), 500);
}
?>
